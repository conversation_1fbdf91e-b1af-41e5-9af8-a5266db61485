(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_ce1bf3._.js", {

"[project]/lib/apiClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use client";
;
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000") || "http://localhost:8000",
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json"
    }
});
// Request interceptor to handle dynamic headers or logging
apiClient.interceptors.request.use((config)=>{
    const token = localStorage.getItem("access_token");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor to handle common errors globally
apiClient.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // Handle specific error responses (e.g., 401 Unauthorized)
    if (error.response) {
        const { status } = error.response;
        if (status === 401) {
            if (window.location.pathname.startsWith("/student") || window.location.pathname.startsWith("/counselor") || localStorage.getItem("access_token")) {
                console.error("Unauthorized. Redirecting to login...");
                localStorage.removeItem("access_token");
                window.location.href = "/auth/login";
            }
        } else if (status >= 500) {
            console.error("Server error:", error.response.data.message || "Internal Server Error");
        }
    } else {
        console.error("Network error:", error.message);
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/student/useProfile.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useProfile": (()=>useProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
"use client";
;
;
;
const useProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        loading: false,
        error: null,
        currentStep: null,
        userInfo: null,
        personalInfo: null,
        educationInfo: null,
        servicesInfo: null,
        setCurrentStep: (step)=>set({
                currentStep: step
            }),
        fetchUserInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`);
                set({
                    userInfo: response.data,
                    loading: false
                });
            } catch (error) {
                set({
                    error: "Failed to fetch user info",
                    loading: false
                });
            }
        },
        fetchPersonalInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/student/profile/personal-info");
                set({
                    personalInfo: response.data,
                    loading: false
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Failed to fetch personal info",
                    loading: false
                });
            }
        },
        fetchEducationalBackground: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/student/profile/educational-background");
                set({
                    educationInfo: response.data.items,
                    loading: false
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Failed to fetch educational background",
                    loading: false
                });
            }
        },
        fetchServicesInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/student/profile/services-info");
                set({
                    servicesInfo: response.data,
                    loading: false
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Failed to fetch Services info",
                    loading: false
                });
            }
        },
        figureCurrentStep: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { fetchPersonalInfo, fetchEducationalBackground } = get();
                await Promise.all([
                    fetchPersonalInfo(),
                    fetchEducationalBackground()
                ]);
                // const { personalInfo, educationInfo, servicesInfo } = get();
                const { personalInfo, educationInfo } = get();
                let currentStep = 1;
                if (personalInfo) {
                    currentStep = 2;
                    if (educationInfo && educationInfo.length > 0) {
                        currentStep = 3;
                    // if (servicesInfo) {
                    //   currentStep = 4;
                    // }
                    }
                }
                set({
                    currentStep,
                    loading: false
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Failed to determine current step",
                    loading: false
                });
            }
        },
        submitPersonalInfo: async (data)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { personalInfo } = get();
                // If data exists, use PUT, else use POST
                const method = personalInfo ? "put" : "post";
                await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][method]("/student/profile/personal-info", data);
                set({
                    personalInfo: data,
                    currentStep: 2
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response?.data?.detail || "Something went wrong");
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        submitEducationInfo: async (data)=>{
            set({
                loading: true,
                error: null
            });
            try {
                const results = await Promise.all(data.map(async (item)=>{
                    const payload = {
                        ...item,
                        end_date: item.is_current ? undefined : item.end_date
                    };
                    if (item.id) {
                        // Update existing entry
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/student/profile/educational-background/${item.id}`, payload);
                        return response.data;
                    } else {
                        // Create new entry
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/student/profile/educational-background", payload);
                        return response.data;
                    }
                }));
                set({
                    educationInfo: results,
                    currentStep: Math.max(get().currentStep || 0, 3),
                    loading: false
                });
                return results;
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Failed to submit education info",
                    loading: false
                });
                throw error;
            }
        },
        updateEducationInfo: async (id, data)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`/student/profile/educational-background/${id}`, data);
                // Update the specific education item in the list
                set((state)=>({
                        educationInfo: state.educationInfo?.map((item)=>item.id === id ? response.data : item) || null
                    }));
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Education info updated successfully");
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response?.data?.detail || "Something went wrong");
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        deleteEducationInfo: async (id)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`/student/profile/educational-background/${id}`);
                // Remove the education item from the list
                set((state)=>({
                        educationInfo: state.educationInfo?.filter((item)=>item.id !== id) || null
                    }));
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Education info deleted successfully");
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response?.data?.detail || "Something went wrong");
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        submitExpectedServices: async (data)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { servicesInfo } = get();
                const method = servicesInfo ? "put" : "post";
                await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][method]("/student/profile/expected-services", data);
                set({
                    servicesInfo: data,
                    currentStep: 4
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error.response?.data?.detail || "Something went wrong");
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        updateprofile_picture_url: (url)=>{
            const currentUserInfo = get().userInfo;
            if (currentUserInfo) {
                set({
                    userInfo: {
                        ...currentUserInfo,
                        profile_picture_url: url
                    }
                });
            }
        },
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/student/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>StudentLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$student$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/hooks/student/useProfile.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
function StudentLayout({ children }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { userInfo, fetchUserInfo } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$student$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProfile"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StudentLayout.useEffect": ()=>{
            if (!localStorage.getItem("access_token")) {
                router.replace("/auth/login");
                return;
            }
            fetchUserInfo();
        }
    }["StudentLayout.useEffect"], [
        fetchUserInfo
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StudentLayout.useEffect": ()=>{
            if (userInfo) {
                // If not a student, redirect to counselor dashboard
                if (userInfo.userType !== "student") {
                    router.replace("/counselor/dashboard");
                    return;
                }
            // Allowing access to all student pages regardless of profile completion status
            }
        }
    }["StudentLayout.useEffect"], [
        userInfo,
        router,
        pathname
    ]);
    return children;
}
_s(StudentLayout, "WEcQ4nbvzhmin6KMR2B9gcwDpJg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$student$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProfile"]
    ];
});
_c = StudentLayout;
var _c;
__turbopack_refresh__.register(_c, "StudentLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/student/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=_ce1bf3._.js.map