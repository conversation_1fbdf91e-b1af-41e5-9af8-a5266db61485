{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/head-wrapper.tsx"], "sourcesContent": ["import Head from \"next/head\";\r\n\r\nexport default function HeadWrapper({\r\n  title,\r\n  description,\r\n}: {\r\n  title: string;\r\n  description?: string;\r\n}) {\r\n  return (\r\n    <Head>\r\n      <title>{`${title || \"\"} | Student's Portal | AdmitPath`}</title>\r\n      <meta\r\n        name=\"description\"\r\n        content={`Student Dashboard Portal for accessing and managing all your sessions and resources. ${\r\n          description || \"\"\r\n        } | AdmitPath`}\r\n      />\r\n    </Head>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,YAAY,EAClC,KAAK,EACL,WAAW,EAIZ;IACC,qBACE,6LAAC,uKAAA,CAAA,UAAI;;0BACH,6LAAC;0BAAO,GAAG,SAAS,GAAG,+BAA+B,CAAC;;;;;;0BACvD,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC,qFAAqF,EAC7F,eAAe,GAChB,YAAY,CAAC;;;;;;;;;;;;AAItB;KAlBwB"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,2BAAa,8JAAM,UAAU,MAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,8KAAoB,IAAI;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,8KAAoB,QAAQ;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,8KAAoB,MAAM;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,8KAAoB,IAAI,CAAC,WAAW;AAE7D,MAAM,0BAAY,8JAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,8KAAoB,mBAAmB;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8KAAoB,eAAe;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,8KAAoB,mBAAmB,CAAC,WAAW"}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/ai-chat/chat-interface.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { Send, Bot, User, Loader2, MessageCircle } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"@/app/components/ui/button\";\nimport { Input } from \"@/app/components/ui/input\";\nimport { Card, CardContent, CardTitle } from \"@/app/components/ui/card\";\nimport { Avatar, AvatarFallback } from \"@/app/components/ui/avatar\";\nimport { ScrollArea } from \"@/app/components/ui/scroll-area\";\nimport { cn } from \"@components/lib/utils\";\n\ninterface Message {\n  id: string;\n  role: \"user\" | \"assistant\";\n  content: string;\n  timestamp: Date;\n  isStreaming?: boolean;\n}\n\ninterface ChatInterfaceProps {\n  className?: string;\n}\n\nexport function ChatInterface({ className }: ChatInterfaceProps) {\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: \"welcome\",\n      role: \"assistant\",\n      content:\n        \"Hello! I'm your AI academic counsellor. I'm here to help you with university admissions, career planning, and academic guidance. What would you like to discuss today?\",\n      timestamp: new Date(),\n    },\n  ]);\n  const [input, setInput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(\n    null\n  );\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  }, [messages]);\n\n  const handleSendMessage = async () => {\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      role: \"user\",\n      content: input.trim(),\n      timestamp: new Date(),\n    };\n\n    const assistantMessageId = (Date.now() + 1).toString();\n    const assistantMessage: Message = {\n      id: assistantMessageId,\n      role: \"assistant\",\n      content: \"\",\n      timestamp: new Date(),\n      isStreaming: true,\n    };\n\n    setMessages((prev) => [...prev, userMessage, assistantMessage]);\n    setInput(\"\");\n    setIsLoading(true);\n    setStreamingMessageId(assistantMessageId);\n\n    try {\n      // Prepare conversation history (exclude the current streaming message)\n      const conversationHistory = messages.map((msg) => ({\n        role: msg.role,\n        content: msg.content,\n      }));\n\n      const response = await fetch(\"/api/ai-counsellor/chat\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          message: userMessage.content,\n          conversationHistory,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(\"Failed to get AI response\");\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error(\"No response stream available\");\n      }\n\n      const decoder = new TextDecoder();\n      let accumulatedContent = \"\";\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value);\n        const lines = chunk.split(\"\\n\");\n\n        for (const line of lines) {\n          if (line.startsWith(\"data: \")) {\n            try {\n              const data = JSON.parse(line.slice(6));\n\n              if (data.error) {\n                throw new Error(data.error);\n              }\n\n              if (data.content) {\n                accumulatedContent += data.content;\n                setMessages((prev) =>\n                  prev.map((msg) =>\n                    msg.id === assistantMessageId\n                      ? { ...msg, content: accumulatedContent }\n                      : msg\n                  )\n                );\n              }\n\n              if (data.done) {\n                setMessages((prev) =>\n                  prev.map((msg) =>\n                    msg.id === assistantMessageId\n                      ? { ...msg, isStreaming: false }\n                      : msg\n                  )\n                );\n                break;\n              }\n            } catch (e) {\n              // Skip invalid JSON lines\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error sending message:\", error);\n      setMessages((prev) =>\n        prev.map((msg) =>\n          msg.id === assistantMessageId\n            ? {\n                ...msg,\n                content: \"Sorry, I encountered an error. Please try again.\",\n                isStreaming: false,\n              }\n            : msg\n        )\n      );\n    } finally {\n      setIsLoading(false);\n      setStreamingMessageId(null);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <Card\n      className={cn(\n        \"flex flex-col h-[calc(100vh-7rem)] md:h-[calc(100vh-15rem)] border-none shadow-md bg-white\",\n        className\n      )}\n    >\n      <div className=\"flex items-center gap-3 p-4 border-b\">\n        <div className=\"p-3 bg-green-100 rounded-full\">\n          <MessageCircle className=\"w-6 h-6 text-green-600\" />\n        </div>\n        <CardTitle className=\"text-lg md:text-xl font-bold text-gray-800\">\n          Chat with AI Counsellor\n        </CardTitle>\n      </div>\n\n      {/* Messages Area */}\n      <ScrollArea className=\"flex-1 p-4\" ref={scrollAreaRef}>\n        <div className=\"space-y-4\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={cn(\n                \"flex gap-3\",\n                message.role === \"user\" ? \"justify-end\" : \"justify-start\"\n              )}\n            >\n              {message.role === \"assistant\" && (\n                <Avatar className=\"h-8 w-8 mt-1\">\n                  <AvatarFallback className=\"bg-blue-100 text-blue-600\">\n                    <Bot className=\"h-4 w-4\" />\n                  </AvatarFallback>\n                </Avatar>\n              )}\n\n              <div\n                className={cn(\n                  \"max-w-[80%] rounded-lg px-4 py-2\",\n                  message.role === \"user\"\n                    ? \"bg-blue-600 text-white\"\n                    : \"bg-gray-100 text-gray-800\"\n                )}\n              >\n                <div className=\"whitespace-pre-wrap break-words\">\n                  {message.content}\n                  {message.isStreaming && (\n                    <span className=\"inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1\" />\n                  )}\n                </div>\n                <div\n                  className={cn(\n                    \"text-xs mt-1 opacity-70\",\n                    message.role === \"user\" ? \"text-blue-100\" : \"text-gray-500\"\n                  )}\n                >\n                  {message.timestamp.toLocaleTimeString([], {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\",\n                  })}\n                </div>\n              </div>\n\n              {message.role === \"user\" && (\n                <Avatar className=\"h-8 w-8 mt-1\">\n                  <AvatarFallback className=\"bg-green-100 text-green-600\">\n                    <User className=\"h-4 w-4\" />\n                  </AvatarFallback>\n                </Avatar>\n              )}\n            </div>\n          ))}\n          <div ref={messagesEndRef} />\n        </div>\n      </ScrollArea>\n\n      {/* Input Area */}\n      <div className=\"p-4 border-t bg-white rounded-b-xl\">\n        <div className=\"flex gap-2\">\n          <Input\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Type your message here...\"\n            disabled={isLoading}\n            className=\"flex-1\"\n            maxLength={1000}\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!input.trim() || isLoading}\n            className=\"bg-blue-600 hover:bg-blue-700\"\n          >\n            {isLoading ? (\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\n            ) : (\n              <Send className=\"h-4 w-4\" />\n            )}\n          </Button>\n        </div>\n        <p className=\"text-xs text-gray-500 mt-2\">\n          {input.length}/1000 characters • Press Enter to send\n        </p>\n      </div>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AANA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;;;;;AAuBO,SAAS,cAAc,EAAE,SAAS,EAAsB;;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SACE;YACF,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD;IAEF,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC7C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;kCAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,MAAM,IAAI;YACnB,WAAW,IAAI;QACjB;QAEA,MAAM,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;QACpD,MAAM,mBAA4B;YAChC,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf,aAAa;QACf;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;gBAAa;aAAiB;QAC9D,SAAS;QACT,aAAa;QACb,sBAAsB;QAEtB,IAAI;YACF,uEAAuE;YACvE,MAAM,sBAAsB,SAAS,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACjD,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;gBACtB,CAAC;YAED,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,YAAY,OAAO;oBAC5B;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,IAAI;YACpB,IAAI,qBAAqB;YAEzB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;gBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;4BAEnC,IAAI,KAAK,KAAK,EAAE;gCACd,MAAM,IAAI,MAAM,KAAK,KAAK;4BAC5B;4BAEA,IAAI,KAAK,OAAO,EAAE;gCAChB,sBAAsB,KAAK,OAAO;gCAClC,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MACR,IAAI,EAAE,KAAK,qBACP;4CAAE,GAAG,GAAG;4CAAE,SAAS;wCAAmB,IACtC;4BAGV;4BAEA,IAAI,KAAK,IAAI,EAAE;gCACb,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MACR,IAAI,EAAE,KAAK,qBACP;4CAAE,GAAG,GAAG;4CAAE,aAAa;wCAAM,IAC7B;gCAGR;4BACF;wBACF,EAAE,OAAO,GAAG;wBACV,0BAA0B;wBAC5B;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MACR,IAAI,EAAE,KAAK,qBACP;wBACE,GAAG,GAAG;wBACN,SAAS;wBACT,aAAa;oBACf,IACA;QAGV,SAAU;YACR,aAAa;YACb,sBAAsB;QACxB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,8FACA;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAA6C;;;;;;;;;;;;0BAMpE,6LAAC,6IAAA,CAAA,aAAU;gBAAC,WAAU;gBAAa,KAAK;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gCAEC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,cACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;;oCAG3C,QAAQ,IAAI,KAAK,6BAChB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;kDAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACxB,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAKrB,6LAAC;wCACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,oCACA,QAAQ,IAAI,KAAK,SACb,2BACA;;0DAGN,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,OAAO;oDACf,QAAQ,WAAW,kBAClB,6LAAC;wDAAK,WAAU;;;;;;;;;;;;0DAGpB,6LAAC;gDACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2BACA,QAAQ,IAAI,KAAK,SAAS,kBAAkB;0DAG7C,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;oDACxC,MAAM;oDACN,QAAQ;gDACV;;;;;;;;;;;;oCAIH,QAAQ,IAAI,KAAK,wBAChB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;kDAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACxB,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;+BA5CjB,QAAQ,EAAE;;;;;sCAkDnB,6LAAC;4BAAI,KAAK;;;;;;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,YAAY;gCACZ,aAAY;gCACZ,UAAU;gCACV,WAAU;gCACV,WAAW;;;;;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,MAAM,IAAI,MAAM;gCAC3B,WAAU;0CAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,6LAAC;wBAAE,WAAU;;4BACV,MAAM,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAKxB;GA3PgB;KAAA"}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/ai-counsellor/chat/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>Left, MessageCircle } from \"lucide-react\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/app/components/ui/card\";\nimport HeadWrapper from \"@/app/components/student/head-wrapper\";\nimport { ChatInterface } from \"@/app/components/student/ai-chat/chat-interface\";\n\nexport default function AIChatPage() {\n  return (\n    <>\n      <HeadWrapper title=\"AI Counsellor - Chat\" />\n      <div className=\"p-2 sm:p-6\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          {/* Chat Interface */}\n          <ChatInterface />\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAOe,SAAS;IACtB,qBACE;;0BACE,6LAAC,mJAAA,CAAA,UAAW;gBAAC,OAAM;;;;;;0BACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC,mKAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;AAKxB;KAZwB"}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/next/src/client/components/noop-head.tsx"], "sourcesContent": ["export default function NoopHead() {\n  return null\n}\n"], "names": ["NoopHead"], "mappings": ";;;;+BAAA,WAAA;;;eAAwBA;;;AAAT,SAASA;IACtB,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,wIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-avatar/src/Avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useImageLoadingStatus(src?: string, referrerPolicy?: React.HTMLAttributeReferrerPolicy) {\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n\n  useLayoutEffect(() => {\n    if (!src) {\n      setLoadingStatus('error');\n      return;\n    }\n\n    let isMounted = true;\n    const image = new window.Image();\n\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      if (!isMounted) return;\n      setLoadingStatus(status);\n    };\n\n    setLoadingStatus('loading');\n    image.onload = updateStatus('loaded');\n    image.onerror = updateStatus('error');\n    image.src = src;\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [src, referrerPolicy]);\n\n  return loadingStatus;\n}\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AAwCf;AAvCR,SAAS,0BAA0B;AAGnC,SAAS,iBAAiB;AAF1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;;;;;;;;AAShC,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,SAAe,8JAAA,UAAA,CACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,GAAU,8JAAA,QAAA,CAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,cAAoB,8JAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,WAAW,cAAc;IAC/E,MAAM,sNAA4B,iBAAA;iEAAe,CAAC,WAA+B;YAC/E,sBAAsB,MAAM;YAC5B,QAAQ,0BAAA,CAA2B,MAAM;QAC3C,CAAC;;IAED,CAAA,GAAA,sLAAA,CAAA,kBAAA;uCAAgB,MAAM;YACpB,IAAI,uBAAuB,QAAQ;gBACjC,0BAA0B,kBAAkB;YAC9C;QACF;sCAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,iBAAuB,8JAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,8JAAA,QAAA,CAAS,YAAY,KAAA,CAAS;IAEhE,8JAAA,SAAA;oCAAU,MAAM;YACpB,IAAI,YAAY,KAAA,GAAW;gBACzB,MAAM,UAAU,OAAO,UAAA;wDAAW,IAAM,aAAa,IAAI;uDAAG,OAAO;gBACnE;gDAAO,IAAM,OAAO,YAAA,CAAa,OAAO;;YAC1C;QACF;mCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,sBAAsB,GAAA,EAAc,cAAA,EAAoD;IAC/F,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,8JAAA,QAAA,CAA6B,MAAM;IAEnF,CAAA,GAAA,sLAAA,CAAA,kBAAA;iDAAgB,MAAM;YACpB,IAAI,CAAC,KAAK;gBACR,iBAAiB,OAAO;gBACxB;YACF;YAEA,IAAI,YAAY;YAChB,MAAM,QAAQ,IAAI,OAAO,KAAA,CAAM;YAE/B,MAAM;sEAAe,CAAC;8EAA+B,MAAM;4BACzD,IAAI,CAAC,UAAW,CAAA;4BAChB,iBAAiB,MAAM;wBACzB;;;YAEA,iBAAiB,SAAS;YAC1B,MAAM,MAAA,GAAS,aAAa,QAAQ;YACpC,MAAM,OAAA,GAAU,aAAa,OAAO;YACpC,MAAM,GAAA,GAAM;YACZ,IAAI,gBAAgB;gBAClB,MAAM,cAAA,GAAiB;YACzB;YAEA;yDAAO,MAAM;oBACX,YAAY;gBACd;;QACF;gDAAG;QAAC;QAAK,cAAc;KAAC;IAExB,OAAO;AACT;AACA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0]}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-scroll-area/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props as { children: React.ReactNode }).children\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    const props = mergeProps(slotProps, children.props as AnyProps);\n    // do not pass ref to React.Fragment for React 19 compatibility\n    if (children.type !== React.Fragment) {\n      props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n    }\n    return React.cloneElement(children, props);\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<React.ComponentProps<typeof Slottable>, typeof Slottable> {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "props"], "mappings": ";;;;;;AAAA,YAAY,WAAW;AAkCjB,SAgDG,YAAAA,WAhDH;AAjCN,SAAS,mBAAmB;;;;AAU5B,IAAM,OAAa,8JAAA,UAAA,CAAmC,CAAC,OAAO,iBAAiB;IAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IACnC,MAAM,gBAAsB,8JAAA,QAAA,CAAS,OAAA,CAAQ,QAAQ;IACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;IAEhD,IAAI,WAAW;QAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;QAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;YAC/C,IAAI,UAAU,WAAW;gBAGvB,IAAU,8JAAA,QAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,OAAa,8JAAA,QAAA,CAAS,IAAA,CAAK,IAAI;gBACzE,OAAa,8JAAA,cAAA,CAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;YACN,OAAO;gBACL,OAAO;YACT;QACF,CAAC;QAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B,UAAM,8JAAA,cAAA,CAAe,UAAU,IACtB,8JAAA,YAAA,CAAa,YAAY,KAAA,GAAW,WAAW,IACrD;QAAA,CACN;IAEJ;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;QAAW,GAAG,SAAA;QAAW,KAAK;QAC5B;IAAA,CACH;AAEJ,CAAC;AAED,KAAK,WAAA,GAAc;AAUnB,IAAM,YAAkB,8JAAA,UAAA,CAAgC,CAAC,OAAO,iBAAiB;IAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IAEnC,IAAU,8JAAA,cAAA,CAAe,QAAQ,GAAG;QAClC,MAAM,cAAc,cAAc,QAAQ;QAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;QAE9D,IAAI,SAAS,IAAA,KAAe,8JAAA,QAAA,EAAU;YACpCA,OAAM,GAAA,GAAM,kMAAe,cAAA,EAAY,cAAc,WAAW,IAAI;QACtE;QACA,OAAa,8JAAA,YAAA,CAAa,UAAUA,MAAK;IAC3C;IAEA,OAAa,8JAAA,QAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,IAAU,8JAAA,QAAA,CAAS,IAAA,CAAK,IAAI,IAAI;AAC1E,CAAC;AAED,UAAU,WAAA,GAAc;AAMxB,IAAM,YAAY,CAAC,EAAE,QAAA,CAAS,CAAA,KAAqC;IACjE,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAAD,WAAAA,EAAA;QAAG;IAAA,CAAS;AACrB;AAMA,SAAS,YACP,KAAA,EACuF;IACvF,OAAa,8JAAA,cAAA,CAAe,KAAK,KAAK,MAAM,IAAA,KAAS;AACvD;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,eAAe,GAAG,IAAI;oBACtB,cAAc,GAAG,IAAI;gBACvB;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF;AAEA,IAAM,OAAO", "ignoreList": [0]}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-scroll-area/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AA2Cf;AA1CX,SAAS,YAAY;;;;;AAErB,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,OAAa,8JAAA,UAAA,CAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,0OAAU,OAAA,GAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,CAAS,qKAAA,SAAA,CAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0]}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,KAAA,EAAe,CAAC,KAAK,GAAG,CAAA,EAA6B;IAClE,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 1257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1263, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-scroll-area/src/scroll-area.tsx", "file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-scroll-area/src/use-state-machine.ts"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useStateMachine } from './use-state-machine';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Sizes = {\n  content: number;\n  viewport: number;\n  scrollbar: {\n    size: number;\n    paddingStart: number;\n    paddingEnd: number;\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollArea\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_AREA_NAME = 'ScrollArea';\n\ntype ScopedProps<P> = P & { __scopeScrollArea?: Scope };\nconst [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\n\ntype ScrollAreaContextValue = {\n  type: 'auto' | 'always' | 'scroll' | 'hover';\n  dir: Direction;\n  scrollHideDelay: number;\n  scrollArea: ScrollAreaElement | null;\n  viewport: ScrollAreaViewportElement | null;\n  onViewportChange(viewport: ScrollAreaViewportElement | null): void;\n  content: HTMLDivElement | null;\n  onContentChange(content: HTMLDivElement): void;\n  scrollbarX: ScrollAreaScrollbarElement | null;\n  onScrollbarXChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarXEnabled: boolean;\n  onScrollbarXEnabledChange(rendered: boolean): void;\n  scrollbarY: ScrollAreaScrollbarElement | null;\n  onScrollbarYChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarYEnabled: boolean;\n  onScrollbarYEnabledChange(rendered: boolean): void;\n  onCornerWidthChange(width: number): void;\n  onCornerHeightChange(height: number): void;\n};\n\nconst [ScrollAreaProvider, useScrollAreaContext] =\n  createScrollAreaContext<ScrollAreaContextValue>(SCROLL_AREA_NAME);\n\ntype ScrollAreaElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ScrollAreaProps extends PrimitiveDivProps {\n  type?: ScrollAreaContextValue['type'];\n  dir?: ScrollAreaContextValue['dir'];\n  scrollHideDelay?: number;\n}\n\nconst ScrollArea = React.forwardRef<ScrollAreaElement, ScrollAreaProps>(\n  (props: ScopedProps<ScrollAreaProps>, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = 'hover',\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React.useState<ScrollAreaElement | null>(null);\n    const [viewport, setViewport] = React.useState<ScrollAreaViewportElement | null>(null);\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const [scrollbarX, setScrollbarX] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [scrollbarY, setScrollbarY] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [cornerWidth, setCornerWidth] = React.useState(0);\n    const [cornerHeight, setCornerHeight] = React.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n\n    return (\n      <ScrollAreaProvider\n        scope={__scopeScrollArea}\n        type={type}\n        dir={direction}\n        scrollHideDelay={scrollHideDelay}\n        scrollArea={scrollArea}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        content={content}\n        onContentChange={setContent}\n        scrollbarX={scrollbarX}\n        onScrollbarXChange={setScrollbarX}\n        scrollbarXEnabled={scrollbarXEnabled}\n        onScrollbarXEnabledChange={setScrollbarXEnabled}\n        scrollbarY={scrollbarY}\n        onScrollbarYChange={setScrollbarY}\n        scrollbarYEnabled={scrollbarYEnabled}\n        onScrollbarYEnabledChange={setScrollbarYEnabled}\n        onCornerWidthChange={setCornerWidth}\n        onCornerHeightChange={setCornerHeight}\n      >\n        <Primitive.div\n          dir={direction}\n          {...scrollAreaProps}\n          ref={composedRefs}\n          style={{\n            position: 'relative',\n            // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n            ['--radix-scroll-area-corner-width' as any]: cornerWidth + 'px',\n            ['--radix-scroll-area-corner-height' as any]: cornerHeight + 'px',\n            ...props.style,\n          }}\n        />\n      </ScrollAreaProvider>\n    );\n  }\n);\n\nScrollArea.displayName = SCROLL_AREA_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ScrollAreaViewport';\n\ntype ScrollAreaViewportElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst ScrollAreaViewport = React.forwardRef<ScrollAreaViewportElement, ScrollAreaViewportProps>(\n  (props: ScopedProps<ScrollAreaViewportProps>, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React.useRef<ScrollAreaViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Primitive.div\n          data-radix-scroll-area-viewport=\"\"\n          {...viewportProps}\n          ref={composedRefs}\n          style={{\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? 'scroll' : 'hidden',\n            overflowY: context.scrollbarYEnabled ? 'scroll' : 'hidden',\n            ...props.style,\n          }}\n        >\n          {/**\n           * `display: table` ensures our content div will match the size of its children in both\n           * horizontal and vertical axis so we can determine if scroll width/height changed and\n           * recalculate thumb sizes. This doesn't account for children with *percentage*\n           * widths that change. We'll wait to see what use-cases consumers come up with there\n           * before trying to resolve it.\n           */}\n          <div ref={context.onContentChange} style={{ minWidth: '100%', display: 'table' }}>\n            {children}\n          </div>\n        </Primitive.div>\n      </>\n    );\n  }\n);\n\nScrollAreaViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaScrollbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLLBAR_NAME = 'ScrollAreaScrollbar';\n\ntype ScrollAreaScrollbarElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbar = React.forwardRef<ScrollAreaScrollbarElement, ScrollAreaScrollbarProps>(\n  (props: ScopedProps<ScrollAreaScrollbarProps>, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === 'horizontal';\n\n    React.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n\n    return context.type === 'hover' ? (\n      <ScrollAreaScrollbarHover {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'scroll' ? (\n      <ScrollAreaScrollbarScroll {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'auto' ? (\n      <ScrollAreaScrollbarAuto {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'always' ? (\n      <ScrollAreaScrollbarVisible {...scrollbarProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarHoverElement = ScrollAreaScrollbarAutoElement;\ninterface ScrollAreaScrollbarHoverProps extends ScrollAreaScrollbarAutoProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarHover = React.forwardRef<\n  ScrollAreaScrollbarHoverElement,\n  ScrollAreaScrollbarHoverProps\n>((props: ScopedProps<ScrollAreaScrollbarHoverProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React.useState(false);\n\n  React.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener('pointerenter', handlePointerEnter);\n      scrollArea.addEventListener('pointerleave', handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener('pointerenter', handlePointerEnter);\n        scrollArea.removeEventListener('pointerleave', handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarAuto\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarScrollElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarScrollProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarScroll = React.forwardRef<\n  ScrollAreaScrollbarScrollElement,\n  ScrollAreaScrollbarScrollProps\n>((props: ScopedProps<ScrollAreaScrollbarScrollProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === 'horizontal';\n  const debounceScrollEnd = useDebounceCallback(() => send('SCROLL_END'), 100);\n  const [state, send] = useStateMachine('hidden', {\n    hidden: {\n      SCROLL: 'scrolling',\n    },\n    scrolling: {\n      SCROLL_END: 'idle',\n      POINTER_ENTER: 'interacting',\n    },\n    interacting: {\n      SCROLL: 'interacting',\n      POINTER_LEAVE: 'idle',\n    },\n    idle: {\n      HIDE: 'hidden',\n      SCROLL: 'scrolling',\n      POINTER_ENTER: 'interacting',\n    },\n  });\n\n  React.useEffect(() => {\n    if (state === 'idle') {\n      const hideTimer = window.setTimeout(() => send('HIDE'), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n\n  React.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? 'scrollLeft' : 'scrollTop';\n\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send('SCROLL');\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n\n  return (\n    <Presence present={forceMount || state !== 'hidden'}>\n      <ScrollAreaScrollbarVisible\n        data-state={state === 'hidden' ? 'hidden' : 'visible'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, () => send('POINTER_ENTER'))}\n        onPointerLeave={composeEventHandlers(props.onPointerLeave, () => send('POINTER_LEAVE'))}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarAutoElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarAutoProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarAuto = React.forwardRef<\n  ScrollAreaScrollbarAutoElement,\n  ScrollAreaScrollbarAutoProps\n>((props: ScopedProps<ScrollAreaScrollbarAutoProps>, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React.useState(false);\n  const isHorizontal = props.orientation === 'horizontal';\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarVisible\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarVisibleElement = ScrollAreaScrollbarAxisElement;\ninterface ScrollAreaScrollbarVisibleProps\n  extends Omit<ScrollAreaScrollbarAxisProps, keyof ScrollAreaScrollbarAxisPrivateProps> {\n  orientation?: 'horizontal' | 'vertical';\n}\n\nconst ScrollAreaScrollbarVisible = React.forwardRef<\n  ScrollAreaScrollbarVisibleElement,\n  ScrollAreaScrollbarVisibleProps\n>((props: ScopedProps<ScrollAreaScrollbarVisibleProps>, forwardedRef) => {\n  const { orientation = 'vertical', ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React.useRef<ScrollAreaThumbElement | null>(null);\n  const pointerOffsetRef = React.useRef(0);\n  const [sizes, setSizes] = React.useState<Sizes>({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 },\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n\n  type UncommonProps = 'onThumbPositionChange' | 'onDragScroll' | 'onWheelScroll';\n  const commonProps: Omit<ScrollAreaScrollbarAxisPrivateProps, UncommonProps> = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => (thumbRef.current = thumb),\n    onThumbPointerUp: () => (pointerOffsetRef.current = 0),\n    onThumbPointerDown: (pointerPos) => (pointerOffsetRef.current = pointerPos),\n  };\n\n  function getScrollPosition(pointerPos: number, dir?: Direction) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n\n  if (orientation === 'horizontal') {\n    return (\n      <ScrollAreaScrollbarX\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }}\n      />\n    );\n  }\n\n  if (orientation === 'vertical') {\n    return (\n      <ScrollAreaScrollbarY\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }}\n      />\n    );\n  }\n\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarAxisPrivateProps = {\n  hasThumb: boolean;\n  sizes: Sizes;\n  onSizesChange(sizes: Sizes): void;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerDown(pointerPos: number): void;\n  onThumbPointerUp(): void;\n  onThumbPositionChange(): void;\n  onWheelScroll(scrollPos: number): void;\n  onDragScroll(pointerPos: number): void;\n};\n\ntype ScrollAreaScrollbarAxisElement = ScrollAreaScrollbarImplElement;\ninterface ScrollAreaScrollbarAxisProps\n  extends Omit<ScrollAreaScrollbarImplProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarAxisPrivateProps {}\n\nconst ScrollAreaScrollbarX = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"horizontal\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        bottom: 0,\n        left: context.dir === 'rtl' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        right: context.dir === 'ltr' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        ['--radix-scroll-area-thumb-width' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.x)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.x)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\nconst ScrollAreaScrollbarY = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"vertical\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        top: 0,\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 'var(--radix-scroll-area-corner-height)',\n        ['--radix-scroll-area-thumb-height' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.y)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.y)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollbarContext = {\n  hasThumb: boolean;\n  scrollbar: ScrollAreaScrollbarElement | null;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerUp(): void;\n  onThumbPointerDown(pointerPos: { x: number; y: number }): void;\n  onThumbPositionChange(): void;\n};\n\nconst [ScrollbarProvider, useScrollbarContext] =\n  createScrollAreaContext<ScrollbarContext>(SCROLLBAR_NAME);\n\ntype ScrollAreaScrollbarImplElement = React.ElementRef<typeof Primitive.div>;\ntype ScrollAreaScrollbarImplPrivateProps = {\n  sizes: Sizes;\n  hasThumb: boolean;\n  onThumbChange: ScrollbarContext['onThumbChange'];\n  onThumbPointerUp: ScrollbarContext['onThumbPointerUp'];\n  onThumbPointerDown: ScrollbarContext['onThumbPointerDown'];\n  onThumbPositionChange: ScrollbarContext['onThumbPositionChange'];\n  onWheelScroll(event: WheelEvent, maxScrollPos: number): void;\n  onDragScroll(pointerPos: { x: number; y: number }): void;\n  onResize(): void;\n};\ninterface ScrollAreaScrollbarImplProps\n  extends Omit<PrimitiveDivProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarImplPrivateProps {}\n\nconst ScrollAreaScrollbarImpl = React.forwardRef<\n  ScrollAreaScrollbarImplElement,\n  ScrollAreaScrollbarImplProps\n>((props: ScopedProps<ScrollAreaScrollbarImplProps>, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React.useState<ScrollAreaScrollbarElement | null>(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React.useRef<DOMRect | null>(null);\n  const prevWebkitUserSelectRef = React.useRef<string>('');\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n\n  function handleDragScroll(event: React.PointerEvent<HTMLElement>) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n\n  /**\n   * We bind wheel event imperatively so we can switch off passive\n   * mode for document wheel event to allow it to be prevented\n   */\n  React.useEffect(() => {\n    const handleWheel = (event: WheelEvent) => {\n      const element = event.target as HTMLElement;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener('wheel', handleWheel, { passive: false });\n    return () => document.removeEventListener('wheel', handleWheel, { passive: false } as any);\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n\n  /**\n   * Update thumb position on sizes change\n   */\n  React.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <ScrollbarProvider\n      scope={__scopeScrollArea}\n      scrollbar={scrollbar}\n      hasThumb={hasThumb}\n      onThumbChange={useCallbackRef(onThumbChange)}\n      onThumbPointerUp={useCallbackRef(onThumbPointerUp)}\n      onThumbPositionChange={handleThumbPositionChange}\n      onThumbPointerDown={useCallbackRef(onThumbPointerDown)}\n    >\n      <Primitive.div\n        {...scrollbarProps}\n        ref={composeRefs}\n        style={{ position: 'absolute', ...scrollbarProps.style }}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const mainPointer = 0;\n          if (event.button === mainPointer) {\n            const element = event.target as HTMLElement;\n            element.setPointerCapture(event.pointerId);\n            rectRef.current = scrollbar!.getBoundingClientRect();\n            // pointer capture doesn't prevent text selection in Safari\n            // so we remove text selection manually when scrolling\n            prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n            document.body.style.webkitUserSelect = 'none';\n            if (context.viewport) context.viewport.style.scrollBehavior = 'auto';\n            handleDragScroll(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, handleDragScroll)}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const element = event.target as HTMLElement;\n          if (element.hasPointerCapture(event.pointerId)) {\n            element.releasePointerCapture(event.pointerId);\n          }\n          document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n          if (context.viewport) context.viewport.style.scrollBehavior = '';\n          rectRef.current = null;\n        })}\n      />\n    </ScrollbarProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'ScrollAreaThumb';\n\ntype ScrollAreaThumbElement = ScrollAreaThumbImplElement;\ninterface ScrollAreaThumbProps extends ScrollAreaThumbImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ScrollAreaThumb = React.forwardRef<ScrollAreaThumbElement, ScrollAreaThumbProps>(\n  (props: ScopedProps<ScrollAreaThumbProps>, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return (\n      <Presence present={forceMount || scrollbarContext.hasThumb}>\n        <ScrollAreaThumbImpl ref={forwardedRef} {...thumbProps} />\n      </Presence>\n    );\n  }\n);\n\ntype ScrollAreaThumbImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaThumbImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaThumbImpl = React.forwardRef<ScrollAreaThumbImplElement, ScrollAreaThumbImplProps>(\n  (props: ScopedProps<ScrollAreaThumbImplProps>, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(forwardedRef, (node) =>\n      scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React.useRef<() => void>(undefined);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = undefined;\n      }\n    }, 100);\n\n    React.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        /**\n         * We only bind to native scroll event so we know when scroll starts and ends.\n         * When scroll starts we start a requestAnimationFrame loop that checks for\n         * changes to scroll position. That rAF loop triggers our thumb position change\n         * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n         * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n         */\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener('scroll', handleScroll);\n        return () => viewport.removeEventListener('scroll', handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n\n    return (\n      <Primitive.div\n        data-state={scrollbarContext.hasThumb ? 'visible' : 'hidden'}\n        {...thumbProps}\n        ref={composedRef}\n        style={{\n          width: 'var(--radix-scroll-area-thumb-width)',\n          height: 'var(--radix-scroll-area-thumb-height)',\n          ...style,\n        }}\n        onPointerDownCapture={composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target as HTMLElement;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)}\n      />\n    );\n  }\n);\n\nScrollAreaThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaCorner\n * -----------------------------------------------------------------------------------------------*/\n\nconst CORNER_NAME = 'ScrollAreaCorner';\n\ntype ScrollAreaCornerElement = ScrollAreaCornerImplElement;\ninterface ScrollAreaCornerProps extends ScrollAreaCornerImplProps {}\n\nconst ScrollAreaCorner = React.forwardRef<ScrollAreaCornerElement, ScrollAreaCornerProps>(\n  (props: ScopedProps<ScrollAreaCornerProps>, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== 'scroll' && hasBothScrollbarsVisible;\n    return hasCorner ? <ScrollAreaCornerImpl {...props} ref={forwardedRef} /> : null;\n  }\n);\n\nScrollAreaCorner.displayName = CORNER_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaCornerImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaCornerImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaCornerImpl = React.forwardRef<\n  ScrollAreaCornerImplElement,\n  ScrollAreaCornerImplProps\n>((props: ScopedProps<ScrollAreaCornerImplProps>, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const hasSize = Boolean(width && height);\n\n  useResizeObserver(context.scrollbarX, () => {\n    const height = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height);\n    setHeight(height);\n  });\n\n  useResizeObserver(context.scrollbarY, () => {\n    const width = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width);\n    setWidth(width);\n  });\n\n  return hasSize ? (\n    <Primitive.div\n      {...cornerProps}\n      ref={forwardedRef}\n      style={{\n        width,\n        height,\n        position: 'absolute',\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 0,\n        ...props.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction toInt(value?: string) {\n  return value ? parseInt(value, 10) : 0;\n}\n\nfunction getThumbRatio(viewportSize: number, contentSize: number) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\n\nfunction getThumbSize(sizes: Sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  // minimum of 18 matches macOS minimum\n  return Math.max(thumbSize, 18);\n}\n\nfunction getScrollPositionFromPointer(\n  pointerPos: number,\n  pointerOffset: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr'\n) {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange as [number, number]);\n  return interpolate(pointerPos);\n}\n\nfunction getThumbOffsetFromScroll(scrollPos: number, sizes: Sizes, dir: Direction = 'ltr') {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange as [number, number]);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction isScrollingWithinScrollbarBounds(scrollPos: number, maxScrollPos: number) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\n\n// Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nconst addUnlinkedScrollListener = (node: HTMLElement, handler = () => {}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\n\nfunction useDebounceCallback(callback: () => void, delay: number) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React.useRef(0);\n  React.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ScrollArea;\nconst Viewport = ScrollAreaViewport;\nconst Scrollbar = ScrollAreaScrollbar;\nconst Thumb = ScrollAreaThumb;\nconst Corner = ScrollAreaCorner;\n\nexport {\n  createScrollAreaScope,\n  //\n  ScrollArea,\n  ScrollAreaViewport,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaCorner,\n  //\n  Root,\n  Viewport,\n  Scrollbar,\n  Thumb,\n  Corner,\n};\nexport type {\n  ScrollAreaProps,\n  ScrollAreaViewportProps,\n  ScrollAreaScrollbarProps,\n  ScrollAreaThumbProps,\n  ScrollAreaCornerProps,\n};\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["React", "height", "width"], "mappings": ";;;;;;;;;;;;;;AAEA,YAAYA,YAAW;;AA6Gf,SAqCF,UArCE,KAqCF,YArCE;AA1GR,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAEhC,SAAS,oBAAoB;AAL7B,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AAOzB,SAAS,4BAA4B;AAJrC,SAAS,sBAAsB;AAG/B,SAAS,aAAa;AADtB,SAAS,uBAAuB;;;;;;;;;;;;;ACEzB,SAAS,gBACd,YAAA,EACA,OAAA,EACA;IACA,OAAa,8JAAA,UAAA;sCAAW,CAAC,OAAwB,UAA4C;YAC3F,MAAM,YAAa,OAAA,CAAQ,KAAK,CAAA,CAAU,KAAK,CAAA;YAC/C,OAAO,aAAa;QACtB;qCAAG,YAAY;AACjB;;ADYA,IAAM,mBAAmB;AAGzB,IAAM,CAAC,yBAAyB,qBAAqB,CAAA,8KAAI,qBAAA,EAAmB,gBAAgB;AAuB5F,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAC7C,wBAAgD,gBAAgB;AAUlE,IAAM,aAAmB,8JAAA,UAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,iBAAA,EACA,OAAO,OAAA,EACP,GAAA,EACA,kBAAkB,GAAA,EAClB,GAAG,iBACL,GAAI;IACJ,MAAM,CAAC,YAAY,aAAa,CAAA,GAAU,8JAAA,QAAA,CAAmC,IAAI;IACjF,MAAM,CAAC,UAAU,WAAW,CAAA,GAAU,8JAAA,QAAA,CAA2C,IAAI;IACrF,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,8JAAA,QAAA,CAAgC,IAAI;IACxE,MAAM,CAAC,YAAY,aAAa,CAAA,GAAU,8JAAA,QAAA,CAA4C,IAAI;IAC1F,MAAM,CAAC,YAAY,aAAa,CAAA,GAAU,8JAAA,QAAA,CAA4C,IAAI;IAC1F,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,8JAAA,QAAA,CAAS,CAAC;IACtD,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,8JAAA,QAAA,CAAS,CAAC;IACxD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,GAAU,8JAAA,QAAA,CAAS,KAAK;IACtE,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,GAAU,8JAAA,QAAA,CAAS,KAAK;IACtE,MAAM,kMAAe,kBAAA,EAAgB;oDAAc,CAAC,OAAS,cAAc,IAAI,CAAC;;IAChF,MAAM,yLAAY,eAAA,EAAa,GAAG;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA;QACA,kBAAkB;QAClB;QACA,iBAAiB;QACjB;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B,qBAAqB;QACrB,sBAAsB;QAEtB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uOAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACJ,GAAG,eAAA;YACJ,KAAK;YACL,OAAO;gBACL,UAAU;gBAAA,0EAAA;gBAEV,CAAC,kCAAyC,CAAA,EAAG,cAAc;gBAC3D,CAAC,mCAA0C,CAAA,EAAG,eAAe;gBAC7D,GAAG,MAAM,KAAA;YACX;QAAA;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,gBAAgB;AAOtB,IAAM,qBAA2B,8JAAA,UAAA,CAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,iBAAA,EAAmB,QAAA,EAAU,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IACjE,MAAM,UAAU,qBAAqB,eAAe,iBAAiB;IACrE,MAAM,MAAY,8JAAA,MAAA,CAAkC,IAAI;IACxD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,KAAK,QAAQ,gBAAgB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;QAEE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;gBACC,yBAAyB;oBACvB,QAAQ,CAAA,mLAAA,CAAA;gBACV;gBACA;YAAA;YAEF,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uOAAC,YAAA,CAAU,GAAA,EAAV;gBACC,mCAAgC;gBAC/B,GAAG,aAAA;gBACJ,KAAK;gBACL,OAAO;oBAAA;;;;;;;;;;aAAA,GAYL,WAAW,QAAQ,iBAAA,GAAoB,WAAW;oBAClD,WAAW,QAAQ,iBAAA,GAAoB,WAAW;oBAClD,GAAG,MAAM,KAAA;gBACX;gBASA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,OAAA;oBAAI,KAAK,QAAQ,eAAA;oBAAiB,OAAO;wBAAE,UAAU;wBAAQ,SAAS;oBAAQ;oBAC5E;gBAAA,CACH;YAAA;SACF;IAAA,CACF;AAEJ;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,iBAAiB;AAOvB,IAAM,sBAA4B,8JAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,EAAE,yBAAA,EAA2B,yBAAA,CAA0B,CAAA,GAAI;IACjE,MAAM,eAAe,MAAM,WAAA,KAAgB;IAErC,8JAAA,SAAA;yCAAU,MAAM;YACpB,eAAe,0BAA0B,IAAI,IAAI,0BAA0B,IAAI;YAC/E;iDAAO,MAAM;oBACX,eAAe,0BAA0B,KAAK,IAAI,0BAA0B,KAAK;gBACnF;;QACF;wCAAG;QAAC;QAAc;QAA2B,yBAAyB;KAAC;IAEvE,OAAO,QAAQ,IAAA,KAAS,UACtB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,0BAAA;QAA0B,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACvF,QAAQ,IAAA,KAAS,WACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,2BAAA;QAA2B,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACxF,QAAQ,IAAA,KAAS,SACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QAAyB,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACtF,QAAQ,IAAA,KAAS,WACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;QAA4B,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc,IACjE;AACN;AAGF,oBAAoB,WAAA,GAAc;AASlC,IAAM,2BAAiC,8JAAA,UAAA,CAGrC,CAAC,OAAmD,iBAAiB;IACrE,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,8JAAA,QAAA,CAAS,KAAK;IAE5C,8JAAA,SAAA;8CAAU,MAAM;YACpB,MAAM,aAAa,QAAQ,UAAA;YAC3B,IAAI,YAAY;YAChB,IAAI,YAAY;gBACd,MAAM;6EAAqB,MAAM;wBAC/B,OAAO,YAAA,CAAa,SAAS;wBAC7B,WAAW,IAAI;oBACjB;;gBACA,MAAM;6EAAqB,MAAM;wBAC/B,YAAY,OAAO,UAAA;qFAAW,IAAM,WAAW,KAAK;oFAAG,QAAQ,eAAe;oBAChF;;gBACA,WAAW,gBAAA,CAAiB,gBAAgB,kBAAkB;gBAC9D,WAAW,gBAAA,CAAiB,gBAAgB,kBAAkB;gBAC9D;0DAAO,MAAM;wBACX,OAAO,YAAA,CAAa,SAAS;wBAC7B,WAAW,mBAAA,CAAoB,gBAAgB,kBAAkB;wBACjE,WAAW,mBAAA,CAAoB,gBAAgB,kBAAkB;oBACnE;;YACF;QACF;6CAAG;QAAC,QAAQ,UAAA;QAAY,QAAQ,eAAe;KAAC;IAEhD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;YACC,cAAY,UAAU,YAAY;YACjC,GAAG,cAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ,CAAC;AAOD,IAAM,4BAAkC,8JAAA,UAAA,CAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,eAAe,MAAM,WAAA,KAAgB;IAC3C,MAAM,oBAAoB;4EAAoB,IAAM,KAAK,YAAY;2EAAG,GAAG;IAC3E,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,gBAAgB,UAAU;QAC9C,QAAQ;YACN,QAAQ;QACV;QACA,WAAW;YACT,YAAY;YACZ,eAAe;QACjB;QACA,aAAa;YACX,QAAQ;YACR,eAAe;QACjB;QACA,MAAM;YACJ,MAAM;YACN,QAAQ;YACR,eAAe;QACjB;IACF,CAAC;IAEK,8JAAA,SAAA;+CAAU,MAAM;YACpB,IAAI,UAAU,QAAQ;gBACpB,MAAM,YAAY,OAAO,UAAA;qEAAW,IAAM,KAAK,MAAM;oEAAG,QAAQ,eAAe;gBAC/E;2DAAO,IAAM,OAAO,YAAA,CAAa,SAAS;;YAC5C;QACF;8CAAG;QAAC;QAAO,QAAQ,eAAA;QAAiB,IAAI;KAAC;IAEnC,8JAAA,SAAA;+CAAU,MAAM;YACpB,MAAM,WAAW,QAAQ,QAAA;YACzB,MAAM,kBAAkB,eAAe,eAAe;YAEtD,IAAI,UAAU;gBACZ,IAAI,gBAAgB,QAAA,CAAS,eAAe,CAAA;gBAC5C,MAAM;wEAAe,MAAM;wBACzB,MAAM,YAAY,QAAA,CAAS,eAAe,CAAA;wBAC1C,MAAM,8BAA8B,kBAAkB;wBACtD,IAAI,6BAA6B;4BAC/B,KAAK,QAAQ;4BACb,kBAAkB;wBACpB;wBACA,gBAAgB;oBAClB;;gBACA,SAAS,gBAAA,CAAiB,UAAU,YAAY;gBAChD;2DAAO,IAAM,SAAS,mBAAA,CAAoB,UAAU,YAAY;;YAClE;QACF;8CAAG;QAAC,QAAQ,QAAA;QAAU;QAAc;QAAM,iBAAiB;KAAC;IAE5D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,UAAU;QACzC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;YACC,cAAY,UAAU,WAAW,WAAW;YAC3C,GAAG,cAAA;YACJ,KAAK;YACL,oLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,IAAM,KAAK,eAAe,CAAC;YACtF,oBAAgB,uLAAA,EAAqB,MAAM,cAAA,EAAgB,IAAM,KAAK,eAAe,CAAC;QAAA;IACxF,CACF;AAEJ,CAAC;AAOD,IAAM,0BAAgC,8JAAA,UAAA,CAGpC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,8JAAA,QAAA,CAAS,KAAK;IAClD,MAAM,eAAe,MAAM,WAAA,KAAgB;IAC3C,MAAM,eAAe;qEAAoB,MAAM;YAC7C,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,cAAc,QAAQ,QAAA,CAAS,WAAA,GAAc,QAAQ,QAAA,CAAS,WAAA;gBACpE,MAAM,cAAc,QAAQ,QAAA,CAAS,YAAA,GAAe,QAAQ,QAAA,CAAS,YAAA;gBACrE,WAAW,eAAe,cAAc,WAAW;YACrD;QACF;oEAAG,EAAE;IAEL,kBAAkB,QAAQ,QAAA,EAAU,YAAY;IAChD,kBAAkB,QAAQ,OAAA,EAAS,YAAY;IAE/C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;YACC,cAAY,UAAU,YAAY;YACjC,GAAG,cAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ,CAAC;AAUD,IAAM,6BAAmC,8JAAA,UAAA,CAGvC,CAAC,OAAqD,iBAAiB;IACvE,MAAM,EAAE,cAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACxD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,WAAiB,8JAAA,MAAA,CAAsC,IAAI;IACjE,MAAM,mBAAyB,8JAAA,MAAA,CAAO,CAAC;IACvC,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAU,8JAAA,QAAA,CAAgB;QAC9C,SAAS;QACT,UAAU;QACV,WAAW;YAAE,MAAM;YAAG,cAAc;YAAG,YAAY;QAAE;IACvD,CAAC;IACD,MAAM,aAAa,cAAc,MAAM,QAAA,EAAU,MAAM,OAAO;IAG9D,MAAM,cAAwE;QAC5E,GAAG,cAAA;QACH;QACA,eAAe;QACf,UAAU,QAAQ,aAAa,KAAK,aAAa,CAAC;QAClD,eAAe,CAAC,QAAW,SAAS,OAAA,GAAU;QAC9C,kBAAkB,IAAO,iBAAiB,OAAA,GAAU;QACpD,oBAAoB,CAAC,aAAgB,iBAAiB,OAAA,GAAU;IAClE;IAEA,SAAS,kBAAkB,UAAA,EAAoB,GAAA,EAAiB;QAC9D,OAAO,6BAA6B,YAAY,iBAAiB,OAAA,EAAS,OAAO,GAAG;IACtF;IAEA,IAAI,gBAAgB,cAAc;QAChC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;YACE,GAAG,WAAA;YACJ,KAAK;YACL,uBAAuB,MAAM;gBAC3B,IAAI,QAAQ,QAAA,IAAY,SAAS,OAAA,EAAS;oBACxC,MAAM,YAAY,QAAQ,QAAA,CAAS,UAAA;oBACnC,MAAM,SAAS,yBAAyB,WAAW,OAAO,QAAQ,GAAG;oBACrE,SAAS,OAAA,CAAQ,KAAA,CAAM,SAAA,GAAY,CAAA,YAAA,EAAe,MAAM,CAAA,SAAA,CAAA;gBAC1D;YACF;YACA,eAAe,CAAC,cAAc;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,UAAA,GAAa;YACtD;YACA,cAAc,CAAC,eAAe;gBAC5B,IAAI,QAAQ,QAAA,EAAU;oBACpB,QAAQ,QAAA,CAAS,UAAA,GAAa,kBAAkB,YAAY,QAAQ,GAAG;gBACzE;YACF;QAAA;IAGN;IAEA,IAAI,gBAAgB,YAAY;QAC9B,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;YACE,GAAG,WAAA;YACJ,KAAK;YACL,uBAAuB,MAAM;gBAC3B,IAAI,QAAQ,QAAA,IAAY,SAAS,OAAA,EAAS;oBACxC,MAAM,YAAY,QAAQ,QAAA,CAAS,SAAA;oBACnC,MAAM,SAAS,yBAAyB,WAAW,KAAK;oBACxD,SAAS,OAAA,CAAQ,KAAA,CAAM,SAAA,GAAY,CAAA,eAAA,EAAkB,MAAM,CAAA,MAAA,CAAA;gBAC7D;YACF;YACA,eAAe,CAAC,cAAc;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,SAAA,GAAY;YACrD;YACA,cAAc,CAAC,eAAe;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,SAAA,GAAY,kBAAkB,UAAU;YACjF;QAAA;IAGN;IAEA,OAAO;AACT,CAAC;AAqBD,IAAM,uBAA6B,8JAAA,UAAA,CAGjC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IACpD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,8JAAA,QAAA,CAA8B;IAC9E,MAAM,MAAY,8JAAA,MAAA,CAAuC,IAAI;IAC7D,MAAM,gMAAc,mBAAA,EAAgB,cAAc,KAAK,QAAQ,kBAAkB;IAE3E,8JAAA,SAAA;0CAAU,MAAM;YACpB,IAAI,IAAI,OAAA,CAAS,CAAA,iBAAiB,iBAAiB,IAAI,OAAO,CAAC;QACjE;yCAAG;QAAC,GAAG;KAAC;IAER,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QACC,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;QACL;QACA,OAAO;YACL,QAAQ;YACR,MAAM,QAAQ,GAAA,KAAQ,QAAQ,0CAA0C;YACxE,OAAO,QAAQ,GAAA,KAAQ,QAAQ,0CAA0C;YACzE,CAAC,iCAAwC,CAAA,EAAG,aAAa,KAAK,IAAI;YAClE,GAAG,MAAM,KAAA;QACX;QACA,oBAAoB,CAAC,aAAe,MAAM,kBAAA,CAAmB,WAAW,CAAC;QACzE,cAAc,CAAC,aAAe,MAAM,YAAA,CAAa,WAAW,CAAC;QAC7D,eAAe,CAAC,OAAO,iBAAiB;YACtC,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,YAAY,QAAQ,QAAA,CAAS,UAAA,GAAa,MAAM,MAAA;gBACtD,MAAM,aAAA,CAAc,SAAS;gBAE7B,IAAI,iCAAiC,WAAW,YAAY,GAAG;oBAC7D,MAAM,cAAA,CAAe;gBACvB;YACF;QACF;QACA,UAAU,MAAM;YACd,IAAI,IAAI,OAAA,IAAW,QAAQ,QAAA,IAAY,eAAe;gBACpD,cAAc;oBACZ,SAAS,QAAQ,QAAA,CAAS,WAAA;oBAC1B,UAAU,QAAQ,QAAA,CAAS,WAAA;oBAC3B,WAAW;wBACT,MAAM,IAAI,OAAA,CAAQ,WAAA;wBAClB,cAAc,MAAM,cAAc,WAAW;wBAC7C,YAAY,MAAM,cAAc,YAAY;oBAC9C;gBACF,CAAC;YACH;QACF;IAAA;AAGN,CAAC;AAED,IAAM,uBAA6B,8JAAA,UAAA,CAGjC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IACpD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,8JAAA,QAAA,CAA8B;IAC9E,MAAM,MAAY,8JAAA,MAAA,CAAuC,IAAI;IAC7D,MAAM,iMAAc,kBAAA,EAAgB,cAAc,KAAK,QAAQ,kBAAkB;IAE3E,8JAAA,SAAA;0CAAU,MAAM;YACpB,IAAI,IAAI,OAAA,CAAS,CAAA,iBAAiB,iBAAiB,IAAI,OAAO,CAAC;QACjE;yCAAG;QAAC,GAAG;KAAC;IAER,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QACC,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;QACL;QACA,OAAO;YACL,KAAK;YACL,OAAO,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YACnC,MAAM,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YAClC,QAAQ;YACR,CAAC,kCAAyC,CAAA,EAAG,aAAa,KAAK,IAAI;YACnE,GAAG,MAAM,KAAA;QACX;QACA,oBAAoB,CAAC,aAAe,MAAM,kBAAA,CAAmB,WAAW,CAAC;QACzE,cAAc,CAAC,aAAe,MAAM,YAAA,CAAa,WAAW,CAAC;QAC7D,eAAe,CAAC,OAAO,iBAAiB;YACtC,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,YAAY,QAAQ,QAAA,CAAS,SAAA,GAAY,MAAM,MAAA;gBACrD,MAAM,aAAA,CAAc,SAAS;gBAE7B,IAAI,iCAAiC,WAAW,YAAY,GAAG;oBAC7D,MAAM,cAAA,CAAe;gBACvB;YACF;QACF;QACA,UAAU,MAAM;YACd,IAAI,IAAI,OAAA,IAAW,QAAQ,QAAA,IAAY,eAAe;gBACpD,cAAc;oBACZ,SAAS,QAAQ,QAAA,CAAS,YAAA;oBAC1B,UAAU,QAAQ,QAAA,CAAS,YAAA;oBAC3B,WAAW;wBACT,MAAM,IAAI,OAAA,CAAQ,YAAA;wBAClB,cAAc,MAAM,cAAc,UAAU;wBAC5C,YAAY,MAAM,cAAc,aAAa;oBAC/C;gBACF,CAAC;YACH;QACF;IAAA;AAGN,CAAC;AAaD,IAAM,CAAC,mBAAmB,mBAAmB,CAAA,GAC3C,wBAA0C,cAAc;AAkB1D,IAAM,0BAAgC,8JAAA,UAAA,CAGpC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EACJ,iBAAA,EACA,KAAA,EACA,QAAA,EACA,aAAA,EACA,gBAAA,EACA,kBAAA,EACA,qBAAA,EACA,YAAA,EACA,aAAA,EACA,QAAA,EACA,GAAG,gBACL,GAAI;IACJ,MAAM,UAAU,qBAAqB,gBAAgB,iBAAiB;IACtE,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,8JAAA,QAAA,CAA4C,IAAI;IACxF,MAAM,gMAAc,mBAAA,EAAgB;gEAAc,CAAC,OAAS,aAAa,IAAI,CAAC;;IAC9E,MAAM,UAAgB,8JAAA,MAAA,CAAuB,IAAI;IACjD,MAAM,0BAAgC,8JAAA,MAAA,CAAe,EAAE;IACvD,MAAM,WAAW,QAAQ,QAAA;IACzB,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,8MAAoB,iBAAA,EAAe,aAAa;IACtD,MAAM,sNAA4B,iBAAA,EAAe,qBAAqB;IACtE,MAAM,eAAe,oBAAoB,UAAU,EAAE;IAErD,SAAS,iBAAiB,KAAA,EAAwC;QAChE,IAAI,QAAQ,OAAA,EAAS;YACnB,MAAM,IAAI,MAAM,OAAA,GAAU,QAAQ,OAAA,CAAQ,IAAA;YAC1C,MAAM,IAAI,MAAM,OAAA,GAAU,QAAQ,OAAA,CAAQ,GAAA;YAC1C,aAAa;gBAAE;gBAAG;YAAE,CAAC;QACvB;IACF;IAMM,8JAAA,SAAA;6CAAU,MAAM;YACpB,MAAM;iEAAc,CAAC,UAAsB;oBACzC,MAAM,UAAU,MAAM,MAAA;oBACtB,MAAM,mBAAmB,WAAW,SAAS,OAAO;oBACpD,IAAI,iBAAkB,CAAA,kBAAkB,OAAO,YAAY;gBAC7D;;YACA,SAAS,gBAAA,CAAiB,SAAS,aAAa;gBAAE,SAAS;YAAM,CAAC;YAClE;qDAAO,IAAM,SAAS,mBAAA,CAAoB,SAAS,aAAa;wBAAE,SAAS;oBAAM,CAAQ;;QAC3F;4CAAG;QAAC;QAAU;QAAW;QAAc,iBAAiB;KAAC;IAKnD,8JAAA,SAAA,CAAU,2BAA2B;QAAC;QAAO,yBAAyB;KAAC;IAE7E,kBAAkB,WAAW,YAAY;IACzC,kBAAkB,QAAQ,OAAA,EAAS,YAAY;IAE/C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACC,OAAO;QACP;QACA;QACA,yMAAe,iBAAA,EAAe,aAAa;QAC3C,4MAAkB,iBAAA,EAAe,gBAAgB;QACjD,uBAAuB;QACvB,wBAAoB,uMAAA,EAAe,kBAAkB;QAErD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uOAAC,YAAA,CAAU,GAAA,EAAV;YACE,GAAG,cAAA;YACJ,KAAK;YACL,OAAO;gBAAE,UAAU;gBAAY,GAAG,eAAe,KAAA;YAAM;YACvD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAClE,MAAM,cAAc;gBACpB,IAAI,MAAM,MAAA,KAAW,aAAa;oBAChC,MAAM,UAAU,MAAM,MAAA;oBACtB,QAAQ,iBAAA,CAAkB,MAAM,SAAS;oBACzC,QAAQ,OAAA,GAAU,UAAW,qBAAA,CAAsB;oBAGnD,wBAAwB,OAAA,GAAU,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA;oBACtD,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA,GAAmB;oBACvC,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,KAAA,CAAM,cAAA,GAAiB;oBAC9D,iBAAiB,KAAK;gBACxB;YACF,CAAC;YACD,kLAAe,wBAAA,EAAqB,MAAM,aAAA,EAAe,gBAAgB;YACzE,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAC9D,MAAM,UAAU,MAAM,MAAA;gBACtB,IAAI,QAAQ,iBAAA,CAAkB,MAAM,SAAS,GAAG;oBAC9C,QAAQ,qBAAA,CAAsB,MAAM,SAAS;gBAC/C;gBACA,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA,GAAmB,wBAAwB,OAAA;gBAC/D,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,KAAA,CAAM,cAAA,GAAiB;gBAC9D,QAAQ,OAAA,GAAU;YACpB,CAAC;QAAA;IACH;AAGN,CAAC;AAMD,IAAM,aAAa;AAWnB,IAAM,kBAAwB,8JAAA,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,UAAA,EAAY,GAAG,WAAW,CAAA,GAAI;IACtC,MAAM,mBAAmB,oBAAoB,YAAY,MAAM,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,iBAAiB,QAAA;QAChD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;YAAoB,KAAK;YAAe,GAAG,UAAA;QAAA,CAAY;IAAA,CAC1D;AAEJ;AAMF,IAAM,sBAA4B,8JAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,iBAAA,EAAmB,KAAA,EAAO,GAAG,WAAW,CAAA,GAAI;IACpD,MAAM,oBAAoB,qBAAqB,YAAY,iBAAiB;IAC5E,MAAM,mBAAmB,oBAAoB,YAAY,iBAAiB;IAC1E,MAAM,EAAE,qBAAA,CAAsB,CAAA,GAAI;IAClC,MAAM,cAAc,qMAAA,EAAgB;4DAAc,CAAC,OACjD,iBAAiB,aAAA,CAAc,IAAI;;IAErC,MAAM,kCAAwC,8JAAA,MAAA,CAAmB,KAAA,CAAS;IAC1E,MAAM,oBAAoB;sEAAoB,MAAM;YAClD,IAAI,gCAAgC,OAAA,EAAS;gBAC3C,gCAAgC,OAAA,CAAQ;gBACxC,gCAAgC,OAAA,GAAU,KAAA;YAC5C;QACF;qEAAG,GAAG;IAEA,8JAAA,SAAA;yCAAU,MAAM;YACpB,MAAM,WAAW,kBAAkB,QAAA;YACnC,IAAI,UAAU;gBAQZ,MAAM;kEAAe,MAAM;wBACzB,kBAAkB;wBAClB,IAAI,CAAC,gCAAgC,OAAA,EAAS;4BAC5C,MAAM,WAAW,0BAA0B,UAAU,qBAAqB;4BAC1E,gCAAgC,OAAA,GAAU;4BAC1C,sBAAsB;wBACxB;oBACF;;gBACA,sBAAsB;gBACtB,SAAS,gBAAA,CAAiB,UAAU,YAAY;gBAChD;qDAAO,IAAM,SAAS,mBAAA,CAAoB,UAAU,YAAY;;YAClE;QACF;wCAAG;QAAC,kBAAkB,QAAA;QAAU;QAAmB,qBAAqB;KAAC;IAEzE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,uOAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAA,GAAW,YAAY;QACnD,GAAG,UAAA;QACJ,KAAK;QACL,OAAO;YACL,OAAO;YACP,QAAQ;YACR,GAAG,KAAA;QACL;QACA,sBAAsB,2LAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,QAAQ,MAAM,MAAA;YACpB,MAAM,YAAY,MAAM,qBAAA,CAAsB;YAC9C,MAAM,IAAI,MAAM,OAAA,GAAU,UAAU,IAAA;YACpC,MAAM,IAAI,MAAM,OAAA,GAAU,UAAU,GAAA;YACpC,iBAAiB,kBAAA,CAAmB;gBAAE;gBAAG;YAAE,CAAC;QAC9C,CAAC;QACD,iBAAa,uLAAA,EAAqB,MAAM,WAAA,EAAa,iBAAiB,gBAAgB;IAAA;AAG5F;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,cAAc;AAKpB,IAAM,mBAAyB,8JAAA,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,UAAU,qBAAqB,aAAa,MAAM,iBAAiB;IACzE,MAAM,2BAA2B,QAAQ,QAAQ,UAAA,IAAc,QAAQ,UAAU;IACjF,MAAM,YAAY,QAAQ,IAAA,KAAS,YAAY;IAC/C,OAAO,YAAY,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QAAsB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc,IAAK;AAC9E;AAGF,iBAAiB,WAAA,GAAc;AAO/B,IAAM,uBAA6B,8JAAA,UAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EAAE,iBAAA,EAAmB,GAAG,YAAY,CAAA,GAAI;IAC9C,MAAM,UAAU,qBAAqB,aAAa,iBAAiB;IACnE,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAU,8JAAA,QAAA,CAAS,CAAC;IAC1C,MAAM,CAAC,QAAQ,SAAS,CAAA,GAAU,8JAAA,QAAA,CAAS,CAAC;IAC5C,MAAM,UAAU,QAAQ,SAAS,MAAM;IAEvC,kBAAkB,QAAQ,UAAA;kDAAY,MAAM;YAC1C,MAAMC,UAAS,QAAQ,UAAA,EAAY,gBAAgB;YACnD,QAAQ,oBAAA,CAAqBA,OAAM;YACnC,UAAUA,OAAM;QAClB,CAAC;;IAED,kBAAkB,QAAQ,UAAA;kDAAY,MAAM;YAC1C,MAAMC,SAAQ,QAAQ,UAAA,EAAY,eAAe;YACjD,QAAQ,mBAAA,CAAoBA,MAAK;YACjC,SAASA,MAAK;QAChB,CAAC;;IAED,OAAO,UACL,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iPAAA,CAAU,GAAA,EAAV;QACE,GAAG,WAAA;QACJ,KAAK;QACL,OAAO;YACL;YACA;YACA,UAAU;YACV,OAAO,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YACnC,MAAM,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YAClC,QAAQ;YACR,GAAG,MAAM,KAAA;QACX;IAAA,KAEA;AACN,CAAC;AAID,SAAS,MAAM,KAAA,EAAgB;IAC7B,OAAO,QAAQ,SAAS,OAAO,EAAE,IAAI;AACvC;AAEA,SAAS,cAAc,YAAA,EAAsB,WAAA,EAAqB;IAChE,MAAM,QAAQ,eAAe;IAC7B,OAAO,MAAM,KAAK,IAAI,IAAI;AAC5B;AAEA,SAAS,aAAa,KAAA,EAAc;IAClC,MAAM,QAAQ,cAAc,MAAM,QAAA,EAAU,MAAM,OAAO;IACzD,MAAM,mBAAmB,MAAM,SAAA,CAAU,YAAA,GAAe,MAAM,SAAA,CAAU,UAAA;IACxE,MAAM,YAAA,CAAa,MAAM,SAAA,CAAU,IAAA,GAAO,gBAAA,IAAoB;IAE9D,OAAO,KAAK,GAAA,CAAI,WAAW,EAAE;AAC/B;AAEA,SAAS,6BACP,UAAA,EACA,aAAA,EACA,KAAA,EACA,MAAiB,KAAA,EACjB;IACA,MAAM,cAAc,aAAa,KAAK;IACtC,MAAM,cAAc,cAAc;IAClC,MAAM,SAAS,iBAAiB;IAChC,MAAM,qBAAqB,cAAc;IACzC,MAAM,gBAAgB,MAAM,SAAA,CAAU,YAAA,GAAe;IACrD,MAAM,gBAAgB,MAAM,SAAA,CAAU,IAAA,GAAO,MAAM,SAAA,CAAU,UAAA,GAAa;IAC1E,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,cAAc,QAAQ,QAAQ;QAAC;QAAG,YAAY;KAAA,GAAI;QAAC,eAAe,CAAA;QAAI,CAAC;KAAA;IAC7E,MAAM,cAAc,YAAY;QAAC;QAAe,aAAa;KAAA,EAAG,WAA+B;IAC/F,OAAO,YAAY,UAAU;AAC/B;AAEA,SAAS,yBAAyB,SAAA,EAAmB,KAAA,EAAc,MAAiB,KAAA,EAAO;IACzF,MAAM,cAAc,aAAa,KAAK;IACtC,MAAM,mBAAmB,MAAM,SAAA,CAAU,YAAA,GAAe,MAAM,SAAA,CAAU,UAAA;IACxE,MAAM,YAAY,MAAM,SAAA,CAAU,IAAA,GAAO;IACzC,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,cAAc,YAAY;IAChC,MAAM,mBAAmB,QAAQ,QAAQ;QAAC;QAAG,YAAY;KAAA,GAAI;QAAC,eAAe,CAAA;QAAI,CAAC;KAAA;IAClF,MAAM,yLAAwB,QAAA,EAAM,WAAW,gBAAoC;IACnF,MAAM,cAAc,YAAY;QAAC;QAAG,YAAY;KAAA,EAAG;QAAC;QAAG,WAAW;KAAC;IACnE,OAAO,YAAY,qBAAqB;AAC1C;AAGA,SAAS,YAAY,KAAA,EAAkC,MAAA,EAAmC;IACxF,OAAO,CAAC,UAAkB;QACxB,IAAI,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,CAAM,CAAC,CAAA,IAAK,MAAA,CAAO,CAAC,CAAA,KAAM,MAAA,CAAO,CAAC,CAAA,CAAG,CAAA,OAAO,MAAA,CAAO,CAAC,CAAA;QACrE,MAAM,QAAA,CAAS,MAAA,CAAO,CAAC,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,IAAA,CAAM,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;QAC3D,OAAO,MAAA,CAAO,CAAC,CAAA,GAAI,QAAA,CAAS,QAAQ,KAAA,CAAM,CAAC,CAAA;IAC7C;AACF;AAEA,SAAS,iCAAiC,SAAA,EAAmB,YAAA,EAAsB;IACjF,OAAO,YAAY,KAAK,YAAY;AACtC;AAIA,IAAM,4BAA4B,CAAC,MAAmB,UAAU,KAAO,CAAD,AAAC,KAAM;IAC3E,IAAI,eAAe;QAAE,MAAM,KAAK,UAAA;QAAY,KAAK,KAAK,SAAA;IAAU;IAChE,IAAI,MAAM;IACV,CAAC,SAAS,OAAO;QACf,MAAM,WAAW;YAAE,MAAM,KAAK,UAAA;YAAY,KAAK,KAAK,SAAA;QAAU;QAC9D,MAAM,qBAAqB,aAAa,IAAA,KAAS,SAAS,IAAA;QAC1D,MAAM,mBAAmB,aAAa,GAAA,KAAQ,SAAS,GAAA;QACvD,IAAI,sBAAsB,iBAAkB,CAAA,QAAQ;QACpD,eAAe;QACf,MAAM,OAAO,qBAAA,CAAsB,IAAI;IACzC,CAAA,EAAG;IACH,OAAO,IAAM,OAAO,oBAAA,CAAqB,GAAG;AAC9C;AAEA,SAAS,oBAAoB,QAAA,EAAsB,KAAA,EAAe;IAChE,MAAM,2MAAiB,iBAAA,EAAe,QAAQ;IAC9C,MAAM,mBAAyB,8JAAA,MAAA,CAAO,CAAC;IACjC,8JAAA,SAAA;yCAAU;iDAAM,IAAM,OAAO,YAAA,CAAa,iBAAiB,OAAO;;wCAAG,CAAC,CAAC;IAC7E,OAAa,8JAAA,WAAA;2CAAY,MAAM;YAC7B,OAAO,YAAA,CAAa,iBAAiB,OAAO;YAC5C,iBAAiB,OAAA,GAAU,OAAO,UAAA,CAAW,gBAAgB,KAAK;QACpE;0CAAG;QAAC;QAAgB,KAAK;KAAC;AAC5B;AAEA,SAAS,kBAAkB,OAAA,EAA6B,QAAA,EAAsB;IAC5E,MAAM,yMAAe,iBAAA,EAAe,QAAQ;IAC5C,CAAA,GAAA,sLAAA,CAAA,kBAAA;6CAAgB,MAAM;YACpB,IAAI,MAAM;YACV,IAAI,SAAS;gBAQX,MAAM,iBAAiB,IAAI;yDAAe,MAAM;wBAC9C,qBAAqB,GAAG;wBACxB,MAAM,OAAO,qBAAA,CAAsB,YAAY;oBACjD,CAAC;;gBACD,eAAe,OAAA,CAAQ,OAAO;gBAC9B;yDAAO,MAAM;wBACX,OAAO,oBAAA,CAAqB,GAAG;wBAC/B,eAAe,SAAA,CAAU,OAAO;oBAClC;;YACF;QACF;4CAAG;QAAC;QAAS,YAAY;KAAC;AAC5B;AAIA,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,QAAQ;AACd,IAAM,SAAS", "ignoreList": [0, 1]}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "file": "message-circle.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n]);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAgB,UAAA,EAAiB,eAAiB,CAAA,CAAA,CAAA;IACtD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChE,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "file": "send.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('Send', [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n]);\n\nexport default Send;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAO,UAAA,EAAiB,MAAQ,CAAA,CAAA,CAAA;IACpC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 2197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAe,UAAA,EAAiB,cAAgB,CAAA,CAAA,CAAA;IACpD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}