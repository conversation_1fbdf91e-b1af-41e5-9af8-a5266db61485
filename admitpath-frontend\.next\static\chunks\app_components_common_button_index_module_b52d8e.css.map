{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/button/index.module.css"], "sourcesContent": [".buttonBase {\r\n\r\n    margin-top: auto;\r\n\r\n    margin-bottom: auto;\r\n\r\n    display: flex;\r\n\r\n    align-items: center;\r\n\r\n    justify-content: center;\r\n\r\n    border-radius: 0.75rem;\r\n\r\n    padding-left: 1.5rem;\r\n\r\n    padding-right: 1.5rem;\r\n\r\n    padding-top: 0.5rem;\r\n\r\n    padding-bottom: 0.5rem;\r\n\r\n    font-size: 0.875rem;\r\n\r\n    font-weight: 500;\r\n\r\n    line-height: 1.5rem;\r\n\r\n    letter-spacing: 0.025em;\r\n\r\n    --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\r\n\r\n    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\r\n\r\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\n}\r\n\r\n.buttonPrimary {\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1))\n}\r\n\r\n.buttonSecondary {\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(55 65 81 / var(--tw-text-opacity, 1))\n}\r\n\r\n.buttonBase:disabled {\r\n\r\n    cursor: not-allowed;\r\n\r\n    opacity: 0.5\n}\r\n\r\n.buttonContent {\r\n\r\n    display: flex;\r\n\r\n    align-items: center;\r\n\r\n    gap: 0.5rem;\r\n\r\n    text-wrap: nowrap\n}\r\n.icon {\r\n\r\n    width: 0.75rem\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;AAqCA;;;;;;;AAWA;;;;;;;AAWA;;;;;AAOA;;;;;;;AAUA"}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}