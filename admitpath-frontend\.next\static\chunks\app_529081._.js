(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/app_529081._.js", {

"[project]/app/hooks/student/useResources.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useResources": (()=>useResources)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
"use client";
;
;
;
const useResources = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        resources: [],
        loading: false,
        error: null,
        page: 1,
        hasMore: true,
        total: 0,
        searchTerm: "",
        category: null,
        audience: null,
        setSearchTerm: (term)=>{
            set({
                searchTerm: term,
                page: 1,
                resources: [],
                hasMore: true
            });
            get().fetchResources();
        },
        setCategory: (category)=>{
            set({
                category,
                page: 1,
                resources: [],
                hasMore: true
            });
            get().fetchResources();
        },
        setAudience: (audience)=>{
            set({
                audience,
                page: 1,
                resources: [],
                hasMore: true
            });
            get().fetchResources();
        },
        fetchResources: async ()=>{
            try {
                const { page, category, searchTerm, audience } = get();
                set({
                    loading: true,
                    error: null
                });
                const queryParams = new URLSearchParams();
                if (category) queryParams.append("category", category);
                if (searchTerm) queryParams.append("search", searchTerm);
                if (audience) queryParams.append("audience", audience);
                queryParams.append("page", page.toString());
                queryParams.append("page_size", "10"); // Fixed page size
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/resources?${queryParams.toString()}`);
                const data = response.data;
                set((state)=>({
                        resources: page === 1 ? data.items : [
                            ...state.resources,
                            ...data.items
                        ],
                        total: data.total,
                        hasMore: data.items.length > 0,
                        loading: false
                    }));
            } catch (error) {
                const errorMessage = error.response?.data?.detail || "Failed to fetch resources";
                set({
                    error: errorMessage,
                    loading: false
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(errorMessage);
            }
        },
        loadMore: ()=>{
            const { loading, hasMore } = get();
            if (!loading && hasMore) {
                set((state)=>({
                        page: state.page + 1
                    }));
                get().fetchResources();
            }
        },
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/public/useCounselors.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useCounselors": (()=>useCounselors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.mjs [app-client] (ecmascript) <locals>");
"use client";
;
;
;
;
const useCounselors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        loading: false,
        profileLoading: false,
        availabilityLoading: false,
        datesLoading: false,
        error: null,
        counselors: null,
        counselorIds: [],
        filters: {},
        currentPage: 1,
        totalPages: 1,
        hasMore: false,
        availableDates: [],
        timeSlots: [],
        setLoading: (loading)=>{
            set({
                loading
            });
        },
        fetchAllCounselors: async ()=>{
            try {
                const { filters, currentPage } = get();
                // Only set loading true if this is the first page
                if (currentPage === 1) {
                    set({
                        loading: true,
                        error: null
                    });
                }
                // Format the parameters for the API call
                const params = {
                    page: currentPage,
                    limit: 16
                };
                // Only add filters that have a value
                if (filters.service_type && filters.service_type.length > 0) {
                    params.service_types = filters.service_type.join(",");
                }
                if (filters.country && filters.country.length > 0) {
                    params.countries = filters.country.join(",");
                }
                if (filters.university && filters.university.length > 0) {
                    params.universities = filters.university.join(",");
                }
                // Only add name if it exists and is not empty
                if (filters.name && filters.name.trim() !== "") {
                    params.name = filters.name.trim();
                }
                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profiles/public-profile/list", {
                    params
                });
                if (data.items && data.items.length > 0) {
                    set((state)=>{
                        // For first page or filter changes, replace the list
                        const isFirstPage = currentPage === 1;
                        return {
                            counselors: isFirstPage ? data.items : [
                                ...state.counselors || [],
                                ...data.items
                            ],
                            counselorIds: isFirstPage ? data.items.map((c)=>c.user_id) : [
                                ...new Set([
                                    ...state.counselorIds,
                                    ...data.items.map((c)=>c.user_id)
                                ])
                            ],
                            totalPages: data.pages,
                            hasMore: data.has_next,
                            loading: false,
                            error: null
                        };
                    });
                } else {
                    set((state)=>({
                            ...state,
                            counselors: currentPage === 1 ? [] : state.counselors,
                            counselorIds: currentPage === 1 ? [] : state.counselorIds,
                            totalPages: currentPage === 1 ? 0 : state.totalPages,
                            hasMore: false,
                            loading: false,
                            error: currentPage === 1 ? "No counselors found" : null
                        }));
                }
            } catch (error) {
                set({
                    error: "Failed to fetch counselors",
                    loading: false
                });
                console.error("Error fetching counselors:", error);
            }
        },
        fetchCounselorProfile: async (userId)=>{
            try {
                set({
                    profileLoading: true,
                    error: null
                });
                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/counselor/profile/public-profile/user/${userId}`);
                // Update counselors state with the fetched profile
                set((state)=>{
                    // Create a new array with existing counselors
                    const updatedCounselors = [
                        ...state.counselors || []
                    ];
                    // Find if this counselor already exists in our array
                    const existingIndex = updatedCounselors.findIndex((c)=>c.user_id === userId);
                    // If counselor exists, update it, otherwise add it
                    if (existingIndex >= 0) {
                        updatedCounselors[existingIndex] = data;
                    } else {
                        updatedCounselors.push(data);
                    }
                    // Update counselorIds if needed
                    let updatedIds = [
                        ...state.counselorIds
                    ];
                    if (!updatedIds.includes(userId)) {
                        updatedIds.push(userId);
                    }
                    return {
                        counselors: updatedCounselors,
                        counselorIds: updatedIds,
                        profileLoading: false
                    };
                });
                return data;
            } catch (error) {
                console.error("Error fetching counselor profile:", error);
                set({
                    error: "Failed to fetch counselor profile",
                    profileLoading: false
                });
                throw error;
            }
        },
        fetchTopCounselors: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profiles/public-profile/list", {
                    params: {
                        page: 1,
                        limit: 8
                    }
                });
                // Check if we have any items before updating state
                if (data.items && data.items.length > 0) {
                    set({
                        counselors: data.items,
                        counselorIds: data.items.map((c)=>c.user_id),
                        loading: false
                    });
                    return data.items;
                } else {
                    set({
                        counselors: [],
                        counselorIds: [],
                        loading: false,
                        error: "No counselors found"
                    });
                    return [];
                }
            } catch (error) {
                set({
                    error: "Failed to fetch top counselors",
                    loading: false,
                    counselors: [],
                    counselorIds: []
                });
                console.error("Error fetching top counselors:", error);
                return [];
            }
        },
        fetchAvailableDates: async (counselorUserId, startDate, endDate, timezone)=>{
            try {
                set({
                    datesLoading: true
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/counselor/availability/public/${counselorUserId}/dates`, {
                    params: {
                        start_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(startDate, "yyyy-MM-dd"),
                        end_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(endDate, "yyyy-MM-dd"),
                        timezone
                    }
                });
                set({
                    availableDates: response.data,
                    datesLoading: false
                });
                return response.data;
            } catch (error) {
                set({
                    error: "Failed to fetch dates",
                    datesLoading: false
                });
                throw error;
            }
        },
        fetchTimeSlots: async (counselorUserId, targetDate, timezone)=>{
            try {
                set({
                    availabilityLoading: true
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/counselor/availability/public/${counselorUserId}/slots`, {
                    params: {
                        target_date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(targetDate, "yyyy-MM-dd"),
                        timezone
                    }
                });
                set({
                    timeSlots: response.data,
                    availabilityLoading: false
                });
                return response.data;
            } catch (error) {
                set({
                    error: "Failed to fetch time slots",
                    availabilityLoading: false
                });
                throw error;
            }
        },
        setFilters: (filters)=>{
            set({
                filters,
                currentPage: 1
            });
        },
        clearFilters: ()=>{
            set({
                filters: {},
                counselors: [],
                currentPage: 1,
                hasMore: false
            });
        },
        loadMore: async ()=>{
            const { currentPage, hasMore } = get();
            if (!hasMore) return;
            set({
                currentPage: currentPage + 1
            });
            await get().fetchAllCounselors();
        }
    }), {
    name: "counselors-storage",
    partialize: (state)=>Object.fromEntries(Object.entries(state).filter(([key])=>[
                "filters"
            ].includes(key)))
}));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/student/useClickOutside.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
;
function useClickOutside(ref, callback) {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useClickOutside.useEffect": ()=>{
            const handleClickOutside = {
                "useClickOutside.useEffect.handleClickOutside": (event)=>{
                    const target = event.touches?.[0]?.target || event.target;
                    if (ref.current && target && !ref.current.contains(target)) {
                        callback();
                    }
                }
            }["useClickOutside.useEffect.handleClickOutside"];
            const options = {
                passive: true
            };
            document.addEventListener("mousedown", handleClickOutside);
            document.addEventListener("touchstart", handleClickOutside, options);
            return ({
                "useClickOutside.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                    document.removeEventListener("touchstart", handleClickOutside);
                }
            })["useClickOutside.useEffect"];
        }
    }["useClickOutside.useEffect"], [
        ref,
        callback
    ]);
}
_s(useClickOutside, "OD7bBpZva5O2jO+Puf00hKivP7c=");
const __TURBOPACK__default__export__ = useClickOutside;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/public/useNewsletter.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useNewsletter": (()=>useNewsletter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
;
;
;
const useNewsletter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set)=>({
        loading: false,
        error: null,
        lastSubscription: null,
        subscribe: async (data)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/newsletter/subscribe", data);
                set({
                    lastSubscription: response.data,
                    error: null
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Successfully subscribed to newsletter!");
            } catch (error) {
                const errorMessage = error.response?.data?.detail || "Failed to subscribe to newsletter";
                set({
                    error: errorMessage,
                    lastSubscription: null
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to subscribe to newsletter");
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/public/useFeaturedCounselors.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useFeaturedCounselors": (()=>useFeaturedCounselors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
;
;
const useFeaturedCounselors = (initialCategory = "top-ivy-league")=>{
    _s();
    const [category, setCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialCategory);
    const [counselors, setCounselors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fetchCounselors = async (selectedCategory)=>{
        setIsLoading(true);
        setError(null);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/counselor/profiles/public-profile/list/featured?category=${selectedCategory}`);
            setCounselors(response.data.items);
        } catch (err) {
            console.error("Error fetching featured counselors:", err);
            setError("Failed to load counselors. Please try again later.");
            setCounselors([]);
        } finally{
            setIsLoading(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useFeaturedCounselors.useEffect": ()=>{
            fetchCounselors(category);
        }
    }["useFeaturedCounselors.useEffect"], [
        category
    ]);
    const changeCategory = (newCategory)=>{
        setCategory(newCategory);
    };
    return {
        counselors,
        isLoading,
        error,
        category,
        changeCategory
    };
};
_s(useFeaturedCounselors, "PNJh7K1bPN+8jghiir9ZB9iTeqw=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/fuzzy-search.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "findClosestMatch": (()=>findClosestMatch),
    "fuzzySearch": (()=>fuzzySearch)
});
function fuzzySearch(needle, haystack) {
    const hLen = haystack.length;
    const nLen = needle.length;
    if (nLen > hLen) {
        return false;
    }
    if (nLen === hLen) {
        return needle.toLowerCase() === haystack.toLowerCase();
    }
    needle = needle.toLowerCase();
    haystack = haystack.toLowerCase();
    let nIdx = 0;
    let hIdx = 0;
    while(nIdx < nLen && hIdx < hLen){
        if (needle[nIdx] === haystack[hIdx]) {
            nIdx++;
        }
        hIdx++;
    }
    return nIdx === nLen;
}
function findClosestMatch(input, options) {
    const matches = options.filter((option)=>fuzzySearch(input, option));
    return matches.length > 0 ? matches[0] : null;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(landing)/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>LandingPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$BecomeCounselor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/BecomeCounselor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$GetExpertHelp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/GetExpertHelp.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Guides$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/Guides.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Hero$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/Hero.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$LogoSlider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/LogoSlider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$newsletter$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/newsletter/index.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Testimonials$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/Testimonials.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$TopCounselors$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/TopCounselors.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$StickyFindCounselor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/StickyFindCounselor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$FeaturedCategories$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/components/public/landing/FeaturedCategories.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
function LandingPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col bg-white font-clash-display",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$StickyFindCounselor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Hero$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$LogoSlider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$TopCounselors$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 20,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$GetExpertHelp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$FeaturedCategories$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$BecomeCounselor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Guides$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$Testimonials$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$public$2f$landing$2f$newsletter$2f$index$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NewsletterForm"], {}, void 0, false, {
                fileName: "[project]/app/(landing)/page.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(landing)/page.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
_c = LandingPage;
var _c;
__turbopack_refresh__.register(_c, "LandingPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(landing)/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=app_529081._.js.map