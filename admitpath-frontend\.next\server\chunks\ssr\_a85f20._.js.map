{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/skeletons.tsx"], "sourcesContent": ["import { Skeleton } from \"@components/ui/skeleton\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport function SidebarSkeleton() {\r\n  return (\r\n    <div className=\"w-full h-full\">\r\n      <div className=\"p-4 border-b\">\r\n        <Skeleton className=\"h-7 w-24 mb-4\" />\r\n        <Skeleton className=\"h-10 w-full rounded-full\" />\r\n      </div>\r\n      <div className=\"p-2\">\r\n        {Array.from({ length: 5 }).map((_, i) => (\r\n          <div key={i} className=\"flex items-center gap-3 p-2\">\r\n            <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n            <div className=\"flex-1\">\r\n              <Skeleton className=\"h-4 w-24 mb-2\" />\r\n              <Skeleton className=\"h-3 w-32\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function HeaderSkeleton() {\r\n  return (\r\n    <div className=\"flex items-center justify-between p-4 bg-white border-b\">\r\n      <div className=\"flex items-center gap-3\">\r\n        <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n        <Skeleton className=\"h-6 w-32\" />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function ChatMessageSkeleton({\r\n  align = \"start\",\r\n}: {\r\n  align?: \"start\" | \"end\";\r\n}) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex gap-3 my-4 mx-3\",\r\n        align === \"end\" ? \"justify-end\" : \"justify-start\"\r\n      )}\r\n    >\r\n      {align === \"start\" && (\r\n        <Skeleton className=\"h-8 w-8 rounded-full shrink-0\" />\r\n      )}\r\n      <div\r\n        className={cn(\r\n          \"flex flex-col gap-2\",\r\n          align === \"end\" ? \"items-end\" : \"items-start\"\r\n        )}\r\n      >\r\n        <Skeleton className=\"h-16 w-[250px] rounded-2xl\" />\r\n        <Skeleton className=\"h-3 w-16\" />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wBAAY,WAAU;;0CACrB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;uBAJd;;;;;;;;;;;;;;;;AAWpB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC,oIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI5B;AAEO,SAAS,oBAAoB,EAClC,QAAQ,OAAO,EAGhB;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wBACA,UAAU,QAAQ,gBAAgB;;YAGnC,UAAU,yBACT,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BAEtB,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uBACA,UAAU,QAAQ,cAAc;;kCAGlC,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI5B"}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/chat-sidebar.tsx"], "sourcesContent": ["import { useState, useMemo } from \"react\";\r\nimport type { Channel } from \"@/app/types/chat\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { SidebarSkeleton } from \"./skeletons\";\r\nimport { X } from \"lucide-react\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Badge } from \"@/app/components/ui/badge\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface ChatSidebarProps {\r\n  channels: Channel[] | null;\r\n  currentChannel: Channel | null;\r\n  onChannelSelect: (channel: Channel) => void;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  loading: boolean;\r\n  unseenMessages?: Record<string, number>;\r\n}\r\n\r\nexport const ChatSidebar = ({\r\n  channels,\r\n  currentChannel,\r\n  onChannelSelect,\r\n  isOpen,\r\n  onClose,\r\n  loading,\r\n  unseenMessages = {},\r\n}: ChatSidebarProps) => {\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  const filteredChannels = useMemo(() => {\r\n    if (!channels) return null;\r\n    return channels.filter(\r\n      (channel) =>\r\n        channel.other_participant.first_name\r\n          .toLowerCase()\r\n          .includes(searchQuery.toLowerCase()) ||\r\n        channel.other_participant.last_name\r\n          .toLowerCase()\r\n          .includes(searchQuery.toLowerCase())\r\n    );\r\n  }, [channels, searchQuery]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"fixed md:relative w-72 border-r h-full bg-white drop-shadow-2xl md:drop-shadow-none \",\r\n        isOpen ? \"translate-x-0\" : \"-translate-x-[120%] md:translate-x-0\",\r\n        \"transition-transform duration-300 ease-in-out md:z-0 z-20\"\r\n      )}\r\n    >\r\n      <div className=\"p-4 md:p-6 border-b flex justify-between items-center\">\r\n        <h1 className=\"text-xl font-semibold\">Messages</h1>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"md:hidden\"\r\n          onClick={onClose}\r\n        >\r\n          <X className=\"h-6 w-6\" />\r\n        </Button>\r\n      </div>\r\n      <div className=\"p-4\">\r\n        <Input\r\n          type=\"search\"\r\n          placeholder=\"Search Chat\"\r\n          className=\"w-full pl-4 pr-4 py-2 rounded-full bg-gray-50\"\r\n          value={searchQuery}\r\n          onChange={(e) => setSearchQuery(e.target.value)}\r\n        />\r\n      </div>\r\n      <div className=\"overflow-y-auto h-[calc(100%-8rem)]\">\r\n        {loading && !filteredChannels ? (\r\n          <SidebarSkeleton />\r\n        ) : filteredChannels && filteredChannels.length === 0 ? (\r\n          <div className=\"p-4 text-center text-gray-500\">\r\n            No matching chats found\r\n          </div>\r\n        ) : (\r\n          filteredChannels?.map((channel) => (\r\n            <div\r\n              key={channel.channel_id}\r\n              onClick={() => {\r\n                if (currentChannel?.channel_id !== channel?.channel_id) {\r\n                  onChannelSelect(channel);\r\n                }\r\n                onClose();\r\n              }}\r\n              className={cn(\r\n                \"flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer\",\r\n                channel.channel_id === currentChannel?.channel_id &&\r\n                  \"bg-gray-50\"\r\n              )}\r\n            >\r\n              <Avatar className=\"h-10 w-10\">\r\n                <AvatarImage\r\n                  src={\r\n                    channel.other_participant.profile_picture_url ||\r\n                    generatePlaceholder(\r\n                      channel.other_participant.first_name,\r\n                      channel.other_participant.last_name\r\n                    )\r\n                  }\r\n                  alt={`${channel.other_participant.first_name} ${channel.other_participant.last_name}`}\r\n                />\r\n                <AvatarFallback>\r\n                  {channel.other_participant.first_name[0]}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <p className=\"text-sm font-medium text-gray-900 truncate\">\r\n                  {`${channel.other_participant.first_name} ${channel.other_participant.last_name}`}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500\">\r\n                  {channel.other_participant.user_type}\r\n                </p>\r\n              </div>\r\n              <div className=\"relative\">\r\n                {unseenMessages[channel.channel_id] > 0 && (\r\n                  <Badge className=\"bg-blue-500 ml-2\">\r\n                    {unseenMessages[channel.channel_id]}\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ))\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;;;;;;;;;;;AAeO,MAAM,cAAc,CAAC,EAC1B,QAAQ,EACR,cAAc,EACd,eAAe,EACf,MAAM,EACN,OAAO,EACP,OAAO,EACP,iBAAiB,CAAC,CAAC,EACF;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,UAAU,OAAO;QACtB,OAAO,SAAS,MAAM,CACpB,CAAC,UACC,QAAQ,iBAAiB,CAAC,UAAU,CACjC,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW,OACnC,QAAQ,iBAAiB,CAAC,SAAS,CAChC,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW;IAEzC,GAAG;QAAC;QAAU;KAAY;IAE1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wFACA,SAAS,kBAAkB,wCAC3B;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,aAAY;oBACZ,WAAU;oBACV,OAAO;oBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;0BAGlD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,CAAC,iCACX,8OAAC,iJAAA,CAAA,kBAAe;;;;2BACd,oBAAoB,iBAAiB,MAAM,KAAK,kBAClD,8OAAC;oBAAI,WAAU;8BAAgC;;;;;2BAI/C,kBAAkB,IAAI,CAAC,wBACrB,8OAAC;wBAEC,SAAS;4BACP,IAAI,gBAAgB,eAAe,SAAS,YAAY;gCACtD,gBAAgB;4BAClB;4BACA;wBACF;wBACA,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA,QAAQ,UAAU,KAAK,gBAAgB,cACrC;;0CAGJ,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kIAAA,CAAA,cAAW;wCACV,KACE,QAAQ,iBAAiB,CAAC,mBAAmB,IAC7C,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,QAAQ,iBAAiB,CAAC,UAAU,EACpC,QAAQ,iBAAiB,CAAC,SAAS;wCAGvC,KAAK,GAAG,QAAQ,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,iBAAiB,CAAC,SAAS,EAAE;;;;;;kDAEvF,8OAAC,kIAAA,CAAA,iBAAc;kDACZ,QAAQ,iBAAiB,CAAC,UAAU,CAAC,EAAE;;;;;;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,GAAG,QAAQ,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,iBAAiB,CAAC,SAAS,EAAE;;;;;;kDAEnF,8OAAC;wCAAE,WAAU;kDACV,QAAQ,iBAAiB,CAAC,SAAS;;;;;;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;0CACZ,cAAc,CAAC,QAAQ,UAAU,CAAC,GAAG,mBACpC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CACd,cAAc,CAAC,QAAQ,UAAU,CAAC;;;;;;;;;;;;uBAvCpC,QAAQ,UAAU;;;;;;;;;;;;;;;;AAiDrC"}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/message-list.tsx"], "sourcesContent": ["import type { Message } from \"@/app/types/chat\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface MessageListProps {\r\n  messages: Message[];\r\n  currentUserId: number;\r\n  isLoadingMore: boolean;\r\n  onLoadMore: () => void;\r\n  hasMoreMessages: boolean;\r\n}\r\n\r\nexport const MessageList = ({\r\n  messages,\r\n  currentUserId,\r\n  isLoadingMore,\r\n  onLoadMore,\r\n  hasMoreMessages,\r\n}: MessageListProps) => {\r\n  const groupMessagesByDate = (messages: Message[]) => {\r\n    const groups: { [key: string]: Message[] } = {};\r\n    const sortedMessages = [...messages].sort(\r\n      (a, b) =>\r\n        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()\r\n    );\r\n    sortedMessages.forEach((message) => {\r\n      const date = new Date(message.created_at);\r\n      // Format as YYYY-MM-DD to ensure consistent grouping\r\n      const dateKey = date.toISOString().split(\"T\")[0];\r\n      if (!groups[dateKey]) {\r\n        groups[dateKey] = [];\r\n      }\r\n      groups[dateKey].push(message);\r\n    });\r\n    return groups;\r\n  };\r\n\r\n  const getDateLabel = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const today = new Date();\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    // Convert to dates without time for comparison\r\n    const dateWithoutTime = new Date(\r\n      date.getFullYear(),\r\n      date.getMonth(),\r\n      date.getDate()\r\n    );\r\n    const todayWithoutTime = new Date(\r\n      today.getFullYear(),\r\n      today.getMonth(),\r\n      today.getDate()\r\n    );\r\n    const yesterdayWithoutTime = new Date(\r\n      yesterday.getFullYear(),\r\n      yesterday.getMonth(),\r\n      yesterday.getDate()\r\n    );\r\n\r\n    if (dateWithoutTime.getTime() === todayWithoutTime.getTime()) {\r\n      return \"Today\";\r\n    } else if (dateWithoutTime.getTime() === yesterdayWithoutTime.getTime()) {\r\n      return \"Yesterday\";\r\n    } else {\r\n      return date.toLocaleDateString(\"en-US\", {\r\n        weekday: \"long\",\r\n        year: \"numeric\",\r\n        month: \"long\",\r\n        day: \"numeric\",\r\n      });\r\n    }\r\n  };\r\n\r\n  if (messages.length === 0) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center h-full text-gray-500\">\r\n        <p>No messages yet</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const groupedMessages = groupMessagesByDate(messages);\r\n\r\n  return (\r\n    <div className=\"flex flex-col p-4 space-y-6\">\r\n      {hasMoreMessages && (\r\n        <div className=\"flex justify-center\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onLoadMore}\r\n            disabled={isLoadingMore}\r\n            className=\"mb-4\"\r\n          >\r\n            {isLoadingMore ? (\r\n              <>\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                Loading...\r\n              </>\r\n            ) : (\r\n              \"Load More Messages\"\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {Object.entries(groupedMessages).map(([date, msgs]) => (\r\n        <div key={date} className=\"space-y-4\">\r\n          <div className=\"sticky top-0 flex justify-center\">\r\n            <span className=\"text-xs md:text-sm bg-gray-100 text-gray-500 px-3 md:px-4 py-1 md:py-2 m-2 rounded-full md:drop-shadow-sm drop-shadow-xl\">\r\n              {getDateLabel(date)}\r\n            </span>\r\n          </div>\r\n          {msgs.map((msg) => (\r\n            <div\r\n              key={msg.id}\r\n              className={cn(\r\n                \"flex gap-3\",\r\n                msg.sender_id === currentUserId\r\n                  ? \"justify-end\"\r\n                  : \"justify-start\"\r\n              )}\r\n            >\r\n              {msg.sender_id !== currentUserId && (\r\n                <Avatar className=\"h-8 w-8 shrink-0\">\r\n                  <AvatarImage\r\n                    src={\r\n                      msg.sender_profile_picture_url ||\r\n                      generatePlaceholder(\r\n                        msg.sender_name.split(\" \")[0],\r\n                        msg.sender_name.split(\" \")[1]\r\n                      )\r\n                    }\r\n                    alt={msg.sender_name}\r\n                  />\r\n                  <AvatarFallback>{msg.sender_name[0]}</AvatarFallback>\r\n                </Avatar>\r\n              )}\r\n              <div\r\n                className={cn(\r\n                  \"max-w-[70%] rounded-2xl px-4 py-2\",\r\n                  msg.sender_id === currentUserId\r\n                    ? \"bg-[#3E84F8] text-white\"\r\n                    : \"bg-white border\"\r\n                )}\r\n              >\r\n                <p className=\"break-words text-sm\">{msg.message}</p>\r\n                <p className=\"text-[10px] mt-1 opacity-70\">\r\n                  {new Date(msg.created_at).toLocaleTimeString([], {\r\n                    hour: \"2-digit\",\r\n                    minute: \"2-digit\",\r\n                  })}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AADA;;;;;;;AAWO,MAAM,cAAc,CAAC,EAC1B,QAAQ,EACR,aAAa,EACb,aAAa,EACb,UAAU,EACV,eAAe,EACE;IACjB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,SAAuC,CAAC;QAC9C,MAAM,iBAAiB;eAAI;SAAS,CAAC,IAAI,CACvC,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAErE,eAAe,OAAO,CAAC,CAAC;YACtB,MAAM,OAAO,IAAI,KAAK,QAAQ,UAAU;YACxC,qDAAqD;YACrD,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACpB,MAAM,CAAC,QAAQ,GAAG,EAAE;YACtB;YACA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvB;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,+CAA+C;QAC/C,MAAM,kBAAkB,IAAI,KAC1B,KAAK,WAAW,IAChB,KAAK,QAAQ,IACb,KAAK,OAAO;QAEd,MAAM,mBAAmB,IAAI,KAC3B,MAAM,WAAW,IACjB,MAAM,QAAQ,IACd,MAAM,OAAO;QAEf,MAAM,uBAAuB,IAAI,KAC/B,UAAU,WAAW,IACrB,UAAU,QAAQ,IAClB,UAAU,OAAO;QAGnB,IAAI,gBAAgB,OAAO,OAAO,iBAAiB,OAAO,IAAI;YAC5D,OAAO;QACT,OAAO,IAAI,gBAAgB,OAAO,OAAO,qBAAqB,OAAO,IAAI;YACvE,OAAO;QACT,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF;IACF;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,MAAM,kBAAkB,oBAAoB;IAE5C,qBACE,8OAAC;QAAI,WAAU;;YACZ,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,8BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;uCAInD;;;;;;;;;;;YAMP,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAChD,8OAAC;oBAAe,WAAU;;sCACxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,aAAa;;;;;;;;;;;wBAGjB,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gCAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,cACA,IAAI,SAAS,KAAK,gBACd,gBACA;;oCAGL,IAAI,SAAS,KAAK,+BACjB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDACV,KACE,IAAI,0BAA0B,IAC9B,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAC7B,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gDAGjC,KAAK,IAAI,WAAW;;;;;;0DAEtB,8OAAC,kIAAA,CAAA,iBAAc;0DAAE,IAAI,WAAW,CAAC,EAAE;;;;;;;;;;;;kDAGvC,8OAAC;wCACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qCACA,IAAI,SAAS,KAAK,gBACd,4BACA;;0DAGN,8OAAC;gDAAE,WAAU;0DAAuB,IAAI,OAAO;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;oDAC/C,MAAM;oDACN,QAAQ;gDACV;;;;;;;;;;;;;+BApCC,IAAI,EAAE;;;;;;mBARP;;;;;;;;;;;AAqDlB"}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/chat-interface.tsx"], "sourcesContent": ["import type React from \"react\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { Menu, Send } from \"lucide-react\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { MessageList } from \"./message-list\";\r\nimport type { UserInfo } from \"@/app/types/student/profile\";\r\nimport type {\r\n  Channel,\r\n  Message,\r\n  WebSocketConnectionStatus,\r\n} from \"@/app/types/chat\";\r\nimport { ChatMessageSkeleton, HeaderSkeleton } from \"./skeletons\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ntype ChatInterfaceProps = {\r\n  userInfo: UserInfo | null;\r\n  currentChannel: Channel | null;\r\n  messages: Message[] | null;\r\n  message: string;\r\n  setMessage: (message: string) => void;\r\n  handleSendMessage: () => void;\r\n  sidebarOpen: boolean;\r\n  setSidebarOpen: (open: boolean) => void;\r\n  loading: boolean;\r\n  fetchMoreMessages: (skip: number) => Promise<void>;\r\n  connectionStatus?: WebSocketConnectionStatus;\r\n  onReconnect?: () => void;\r\n  isConnected?: boolean;\r\n};\r\n\r\nconst ChatInterface = ({\r\n  userInfo,\r\n  currentChannel,\r\n  messages,\r\n  message,\r\n  setMessage,\r\n  handleSendMessage,\r\n  sidebarOpen,\r\n  setSidebarOpen,\r\n  loading,\r\n  fetchMoreMessages,\r\n  connectionStatus, // Kept for functionality but not displayed in UI\r\n  onReconnect, // Kept for functionality but not displayed in UI\r\n  isConnected, // Kept for compatibility but not used to disable input\r\n}: ChatInterfaceProps) => {\r\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\r\n  const [hasMoreMessages, setHasMoreMessages] = useState(true);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const messageContainerRef = useRef<HTMLDivElement>(null);\r\n  const previousMessagesLengthRef = useRef(messages?.length || 0);\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n\r\n  const scrollToBottom = () => {\r\n    messageContainerRef.current?.scrollTo({\r\n      top: messageContainerRef.current.scrollHeight,\r\n      behavior: \"smooth\",\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (messages && messages.length > 0 && isInitialLoad) {\r\n      scrollToBottom();\r\n      setIsInitialLoad(false);\r\n    }\r\n  }, [messages, isInitialLoad]);\r\n\r\n  useEffect(() => {\r\n    if (!messages || isInitialLoad) return;\r\n\r\n    const currentLength = messages.length;\r\n    const previousLength = previousMessagesLengthRef.current;\r\n\r\n    if (currentLength === previousLength + 1 && !isLoadingMore) {\r\n      scrollToBottom();\r\n    }\r\n\r\n    previousMessagesLengthRef.current = currentLength;\r\n  }, [messages, isLoadingMore, isInitialLoad]);\r\n\r\n  const handleLoadMore = async () => {\r\n    if (!isLoadingMore && messages?.length) {\r\n      setIsLoadingMore(true);\r\n      try {\r\n        await fetchMoreMessages(messages.length);\r\n        // Check if we got fewer messages than the limit (15)\r\n        // If so, we've reached the end\r\n        if (messages.length % 15 !== 0) {\r\n          setHasMoreMessages(false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading more messages:\", error);\r\n      } finally {\r\n        setIsLoadingMore(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderHeader = () => {\r\n    if (!currentChannel?.other_participant) {\r\n      return <HeaderSkeleton />;\r\n    }\r\n    if (currentChannel?.other_participant) {\r\n      const participant = currentChannel.other_participant;\r\n      return (\r\n        <div className=\"flex items-center justify-between p-2 bg-white border-b \">\r\n          <div className=\"flex items-center gap-3\">\r\n            <button\r\n              className=\"md:hidden p-2 text-gray-900 focus:outline-none rounded-lg bg-white\"\r\n              onClick={() => setSidebarOpen(!sidebarOpen)}\r\n            >\r\n              <Menu className=\"w-6 h-6\" />\r\n            </button>\r\n            <div\r\n              className={cn(\r\n                \"flex gap-4 justify-center items-center rounded-lg hover:bg-gray-100 transition-all p-2\",\r\n                participant.user_type === \"counselor\" && \"cursor-pointer\"\r\n              )}\r\n              onClick={() => {\r\n                participant.user_type === \"counselor\" &&\r\n                  window.location.replace(`/profile/${participant.user_id}`);\r\n              }}\r\n            >\r\n              <Avatar className=\"h-10 w-10\">\r\n                <AvatarImage\r\n                  src={\r\n                    participant.profile_picture_url ||\r\n                    generatePlaceholder(\r\n                      participant.first_name,\r\n                      participant.last_name\r\n                    )\r\n                  }\r\n                  alt={`${participant.first_name} ${participant.last_name}`}\r\n                />\r\n                <AvatarFallback>{participant.first_name[0]}</AvatarFallback>\r\n              </Avatar>\r\n              <div>\r\n                <h2 className=\"text-lg font-semibold\">{`${participant.first_name} ${participant.last_name}`}</h2>\r\n                <p className=\"text-sm text-gray-500\">{participant.user_type}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return <></>;\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col h-full bg-gray-50 \">\r\n      {renderHeader()}\r\n\r\n      <div ref={messageContainerRef} className=\"flex-1 overflow-y-auto\">\r\n        {(loading && !isLoadingMore) || !messages ? (\r\n          <>\r\n            <ChatMessageSkeleton />\r\n            <ChatMessageSkeleton />\r\n          </>\r\n        ) : (\r\n          <MessageList\r\n            messages={messages}\r\n            currentUserId={userInfo?.user_id || 0}\r\n            isLoadingMore={isLoadingMore}\r\n            onLoadMore={handleLoadMore}\r\n            hasMoreMessages={hasMoreMessages}\r\n          />\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      {currentChannel && (\r\n        <div className=\"p-4 bg-white border-t\">\r\n          <div className=\"flex items-center gap-2 max-w-4xl mx-auto\">\r\n            {/* <Button variant=\"ghost\" size=\"icon\" className=\"shrink-0\">\r\n              <Smile className=\"h-6 w-6\" />\r\n            </Button> */}\r\n            <Input\r\n              value={message}\r\n              onChange={(e) => setMessage(e.target.value)}\r\n              placeholder=\"Type a message...\"\r\n              className=\"flex-1\"\r\n              onKeyDown={(e) => {\r\n                if (e.key === \"Enter\" && !e.shiftKey) {\r\n                  e.preventDefault();\r\n                  handleSendMessage();\r\n                }\r\n              }}\r\n            />\r\n            <Button\r\n              size=\"icon\"\r\n              className=\"bg-[#1E2875] text-white hover:bg-[#1E2875]/90 shrink-0\"\r\n              onClick={handleSendMessage}\r\n              disabled={!message.trim()}\r\n            >\r\n              <Send className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInterface;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAbA;AAAA;;;;;;;;;;;AA+BA,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,OAAO,EACP,UAAU,EACV,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,OAAO,EACP,iBAAiB,EACjB,gBAAgB,EAChB,WAAW,EACX,WAAW,EACQ;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,UAAU,UAAU;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB;QACrB,oBAAoB,OAAO,EAAE,SAAS;YACpC,KAAK,oBAAoB,OAAO,CAAC,YAAY;YAC7C,UAAU;QACZ;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,SAAS,MAAM,GAAG,KAAK,eAAe;YACpD;YACA,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAU;KAAc;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,eAAe;QAEhC,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,iBAAiB,0BAA0B,OAAO;QAExD,IAAI,kBAAkB,iBAAiB,KAAK,CAAC,eAAe;YAC1D;QACF;QAEA,0BAA0B,OAAO,GAAG;IACtC,GAAG;QAAC;QAAU;QAAe;KAAc;IAE3C,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB,UAAU,QAAQ;YACtC,iBAAiB;YACjB,IAAI;gBACF,MAAM,kBAAkB,SAAS,MAAM;gBACvC,qDAAqD;gBACrD,+BAA+B;gBAC/B,IAAI,SAAS,MAAM,GAAG,OAAO,GAAG;oBAC9B,mBAAmB;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD,SAAU;gBACR,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB,mBAAmB;YACtC,qBAAO,8OAAC,iJAAA,CAAA,iBAAc;;;;;QACxB;QACA,IAAI,gBAAgB,mBAAmB;YACrC,MAAM,cAAc,eAAe,iBAAiB;YACpD,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,eAAe,CAAC;sCAE/B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0FACA,YAAY,SAAS,KAAK,eAAe;4BAE3C,SAAS;gCACP,YAAY,SAAS,KAAK,eACxB,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,YAAY,OAAO,EAAE;4BAC7D;;8CAEA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CACV,KACE,YAAY,mBAAmB,IAC/B,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,YAAY,UAAU,EACtB,YAAY,SAAS;4CAGzB,KAAK,GAAG,YAAY,UAAU,CAAC,CAAC,EAAE,YAAY,SAAS,EAAE;;;;;;sDAE3D,8OAAC,kIAAA,CAAA,iBAAc;sDAAE,YAAY,UAAU,CAAC,EAAE;;;;;;;;;;;;8CAE5C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAyB,GAAG,YAAY,UAAU,CAAC,CAAC,EAAE,YAAY,SAAS,EAAE;;;;;;sDAC3F,8OAAC;4CAAE,WAAU;sDAAyB,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAMvE;QAEA,qBAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ;0BAED,8OAAC;gBAAI,KAAK;gBAAqB,WAAU;;oBACrC,WAAW,CAAC,iBAAkB,CAAC,yBAC/B;;0CACE,8OAAC,iJAAA,CAAA,sBAAmB;;;;;0CACpB,8OAAC,iJAAA,CAAA,sBAAmB;;;;;;qDAGtB,8OAAC,uJAAA,CAAA,cAAW;wBACV,UAAU;wBACV,eAAe,UAAU,WAAW;wBACpC,eAAe;wBACf,YAAY;wBACZ,iBAAiB;;;;;;kCAGrB,8OAAC;wBAAI,KAAK;;;;;;;;;;;;YAGX,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAIb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,aAAY;4BACZ,WAAU;4BACV,WAAW,CAAC;gCACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oCACpC,EAAE,cAAc;oCAChB;gCACF;4BACF;;;;;;sCAEF,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,SAAS;4BACT,UAAU,CAAC,QAAQ,IAAI;sCAEvB,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;uCAEe"}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/websocket-chat.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport {\r\n  useWebSocketChat,\r\n  setWebSocketChatUserInfo,\r\n} from \"@/app/hooks/useWebSocketChat\";\r\nimport { ChatSidebar } from \"./chat-sidebar\";\r\nimport ChatInterface from \"./chat-interface\";\r\n\r\nexport default function WebSocketMessages({\r\n  useProfileHook,\r\n}: {\r\n  useProfileHook: any;\r\n}) {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [message, setMessage] = useState(\"\");\r\n  const {\r\n    channels,\r\n    currentChannel,\r\n    messages,\r\n    loading,\r\n    connectionStatus,\r\n    fetchChannels,\r\n    setCurrentChannel,\r\n    sendMessage,\r\n    fetchMessages,\r\n    reconnectWebSocket,\r\n    isWebSocketConnected,\r\n    getUnseenChannelsCount,\r\n    unseenMessages,\r\n  } = useWebSocketChat();\r\n\r\n  const { userInfo } = useProfileHook();\r\n\r\n  useEffect(() => {\r\n    if (userInfo && userInfo.user_id) {\r\n      setWebSocketChatUserInfo({ user_id: userInfo.user_id });\r\n    }\r\n  }, [userInfo]);\r\n\r\n  useEffect(() => {\r\n    if (!localStorage.getItem(\"access_token\")) {\r\n      window.location.href = \"/auth/login\";\r\n      return;\r\n    }\r\n\r\n    const initializeChat = async () => {\r\n      await fetchChannels();\r\n      await getUnseenChannelsCount();\r\n      if (channels && channels.length > 0 && !currentChannel) {\r\n        setCurrentChannel(channels[0]);\r\n      }\r\n    };\r\n\r\n    initializeChat();\r\n\r\n    return () => {\r\n      if (currentChannel) {\r\n        setCurrentChannel(null);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (channels && channels.length > 0 && !currentChannel) {\r\n      setCurrentChannel(channels[0]);\r\n    }\r\n  }, [channels, currentChannel]);\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!message.trim() || !currentChannel || !userInfo) return;\r\n\r\n    try {\r\n      await sendMessage({\r\n        channel_id: currentChannel.channel_id,\r\n        recipient_id: currentChannel.other_participant.user_id,\r\n        message: message.trim(),\r\n        sender_id: userInfo.user_id,\r\n        sender_name: `${userInfo.firstName} ${userInfo.lastName}`,\r\n      });\r\n      setMessage(\"\");\r\n    } catch (error) {\r\n      console.error(\"Failed to send message:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchMoreMessages = async (skip: number) => {\r\n    if (currentChannel) {\r\n      await fetchMessages(currentChannel.channel_id, skip);\r\n    }\r\n  };\r\n\r\n  const handleReconnect = () => {\r\n    reconnectWebSocket();\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-[calc(100vh-7rem)] md:h-[calc(100vh-9rem)] shadow-sm\">\r\n      <div className=\"flex h-full rounded-xl overflow-hidden\">\r\n        <ChatSidebar\r\n          channels={channels}\r\n          currentChannel={currentChannel}\r\n          onChannelSelect={setCurrentChannel}\r\n          isOpen={sidebarOpen}\r\n          onClose={() => setSidebarOpen(false)}\r\n          loading={loading}\r\n          unseenMessages={unseenMessages}\r\n        />\r\n        <ChatInterface\r\n          loading={loading}\r\n          userInfo={userInfo}\r\n          currentChannel={currentChannel}\r\n          messages={messages}\r\n          message={message}\r\n          setMessage={setMessage}\r\n          handleSendMessage={handleSendMessage}\r\n          sidebarOpen={sidebarOpen}\r\n          setSidebarOpen={setSidebarOpen}\r\n          fetchMoreMessages={fetchMoreMessages}\r\n          connectionStatus={connectionStatus}\r\n          onReconnect={handleReconnect}\r\n          isConnected={isWebSocketConnected()}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AARA;;;;;;AAUe,SAAS,kBAAkB,EACxC,cAAc,EAGf;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,OAAO,EACP,gBAAgB,EAChB,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,oBAAoB,EACpB,sBAAsB,EACtB,cAAc,EACf,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,SAAS,OAAO,EAAE;YAChC,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE;gBAAE,SAAS,SAAS,OAAO;YAAC;QACvD;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,CAAC,iBAAiB;YACzC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACvB;QACF;QAEA,MAAM,iBAAiB;YACrB,MAAM;YACN,MAAM;YACN,IAAI,YAAY,SAAS,MAAM,GAAG,KAAK,CAAC,gBAAgB;gBACtD,kBAAkB,QAAQ,CAAC,EAAE;YAC/B;QACF;QAEA;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,kBAAkB;YACpB;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,SAAS,MAAM,GAAG,KAAK,CAAC,gBAAgB;YACtD,kBAAkB,QAAQ,CAAC,EAAE;QAC/B;IACF,GAAG;QAAC;QAAU;KAAe;IAE7B,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,kBAAkB,CAAC,UAAU;QAErD,IAAI;YACF,MAAM,YAAY;gBAChB,YAAY,eAAe,UAAU;gBACrC,cAAc,eAAe,iBAAiB,CAAC,OAAO;gBACtD,SAAS,QAAQ,IAAI;gBACrB,WAAW,SAAS,OAAO;gBAC3B,aAAa,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;YAC3D;YACA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,gBAAgB;YAClB,MAAM,cAAc,eAAe,UAAU,EAAE;QACjD;IACF;IAEA,MAAM,kBAAkB;QACtB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,uJAAA,CAAA,cAAW;oBACV,UAAU;oBACV,gBAAgB;oBAChB,iBAAiB;oBACjB,QAAQ;oBACR,SAAS,IAAM,eAAe;oBAC9B,SAAS;oBACT,gBAAgB;;;;;;8BAElB,8OAAC,yJAAA,CAAA,UAAa;oBACZ,SAAS;oBACT,UAAU;oBACV,gBAAgB;oBAChB,UAAU;oBACV,SAAS;oBACT,YAAY;oBACZ,mBAAmB;oBACnB,aAAa;oBACb,gBAAgB;oBAChB,mBAAmB;oBACnB,kBAAkB;oBAClB,aAAa;oBACb,aAAa;;;;;;;;;;;;;;;;;AAKvB"}}, {"offset": {"line": 1191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/chat/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport WebSocketMessages from \"./websocket-chat\";\r\n\r\nexport default function Messages({ useProfileHook }: { useProfileHook: any }) {\r\n  return <WebSocketMessages useProfileHook={useProfileHook} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,SAAS,EAAE,cAAc,EAA2B;IAC1E,qBAAO,8OAAC,yJAAA,CAAA,UAAiB;QAAC,gBAAgB;;;;;;AAC5C"}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/messages/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Messages from \"@/app/components/common/chat\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\n\r\nexport default function MessagesPage() {\r\n  return <Messages useProfileHook={useProfile} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBAAO,8OAAC,6IAAA,CAAA,UAAQ;QAAC,gBAAgB,qIAAA,CAAA,aAAU;;;;;;AAC7C"}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/%40radix-ui/react-avatar/src/Avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useImageLoadingStatus(src?: string, referrerPolicy?: React.HTMLAttributeReferrerPolicy) {\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n\n  useLayoutEffect(() => {\n    if (!src) {\n      setLoadingStatus('error');\n      return;\n    }\n\n    let isMounted = true;\n    const image = new window.Image();\n\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      if (!isMounted) return;\n      setLoadingStatus(status);\n    };\n\n    setLoadingStatus('loading');\n    image.onload = updateStatus('loaded');\n    image.onerror = updateStatus('error');\n    image.src = src;\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [src, referrerPolicy]);\n\n  return loadingStatus;\n}\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AAwCf;AAvCR,SAAS,0BAA0B;AAGnC,SAAS,iBAAiB;AAF1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;;;;;;;;AAShC,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,2KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,SAAe,sMAAA,UAAA,CACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,GAAU,sMAAA,QAAA,CAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,WAAW,cAAc;IAC/E,MAAM,mNAA4B,iBAAA,EAAe,CAAC,WAA+B;QAC/E,sBAAsB,MAAM;QAC5B,QAAQ,0BAAA,CAA2B,MAAM;IAC3C,CAAC;IAED,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,uBAAuB,QAAQ;YACjC,0BAA0B,kBAAkB;QAC9C;IACF,GAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,YAAY,KAAA,CAAS;IAEhE,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,UAAU,OAAO,UAAA,CAAW,IAAM,aAAa,IAAI,GAAG,OAAO;YACnE,OAAO,IAAM,OAAO,YAAA,CAAa,OAAO;QAC1C;IACF,GAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,sBAAsB,GAAA,EAAc,cAAA,EAAoD;IAC/F,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,sMAAA,QAAA,CAA6B,MAAM;IAEnF,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,CAAC,KAAK;YACR,iBAAiB,OAAO;YACxB;QACF;QAEA,IAAI,YAAY;QAChB,MAAM,QAAQ,IAAI,OAAO,KAAA,CAAM;QAE/B,MAAM,eAAe,CAAC,SAA+B,MAAM;gBACzD,IAAI,CAAC,UAAW,CAAA;gBAChB,iBAAiB,MAAM;YACzB;QAEA,iBAAiB,SAAS;QAC1B,MAAM,MAAA,GAAS,aAAa,QAAQ;QACpC,MAAM,OAAA,GAAU,aAAa,OAAO;QACpC,MAAM,GAAA,GAAM;QACZ,IAAI,gBAAgB;YAClB,MAAM,cAAA,GAAiB;QACzB;QAEA,OAAO,MAAM;YACX,YAAY;QACd;IACF,GAAG;QAAC;QAAK,cAAc;KAAC;IAExB,OAAO;AACT;AACA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0]}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,qIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('X', [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n]);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wKAAI,UAAA,EAAiB,GAAK,CAAA,CAAA,CAAA;IAC9B;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wKAAe,UAAA,EAAiB,cAAgB,CAAA,CAAA,CAAA;IACpD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1504, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "file": "send.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('Send', [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n]);\n\nexport default Send;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,wKAAO,UAAA,EAAiB,MAAQ,CAAA,CAAA,CAAA;IACpC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}