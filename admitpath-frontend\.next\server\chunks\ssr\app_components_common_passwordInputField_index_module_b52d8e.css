/* [project]/app/components/common/passwordInputField/index.module.css [app-client] (css) */
.index-module__zj4Tga__passwordContainer {
  position: relative;
}

.index-module__zj4Tga__inputIcon {
  position: absolute;
  right: 1rem;
  top: 2.3rem;
  z-index: 10;
  cursor: pointer;
  border-style: none;
  background-color: #0000;
  padding: .25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__eyeIcon {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__eyeIcon:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.index-module__zj4Tga__passwordTooltip {
  position: absolute;
  top: 2.5rem;
  right: 0;
  z-index: 10;
  margin-top: .5rem;
  width: 250px;
  border-radius: .5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.index-module__zj4Tga__passwordTooltip ul {
  list-style-type: none;
}

.index-module__zj4Tga__passwordTooltip ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}

.index-module__zj4Tga__passwordTooltip li:before {
  content: "✔";
  margin-right: .5rem;
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

/*# sourceMappingURL=app_components_common_passwordInputField_index_module_b52d8e.css.map*/