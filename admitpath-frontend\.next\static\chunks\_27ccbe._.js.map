{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,2KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG"}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/logo-loader.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\ninterface LogoLoaderProps {\r\n  size?: number;\r\n  text?: string;\r\n}\r\n\r\nexport function LogoLoader({ size = 40, text }: LogoLoaderProps) {\r\n  return (\r\n    <div className=\"flex flex-col justify-center items-center h-max gap-y-4\">\r\n      <div className=\"relative inline-flex items-center justify-center p-2\">\r\n        <Image\r\n          src=\"/icons/logo.png\"\r\n          alt=\"logo\"\r\n          width={size}\r\n          height={size}\r\n          className=\"rounded-full\"\r\n        />\r\n        <div\r\n          className=\"absolute rounded-full border-4 border-t-transparent animate-spin\"\r\n          style={{\r\n            width: \"120%\",\r\n            height: \"120%\",\r\n            borderColor: \"rgba(239, 68, 68, 0.2)\",\r\n            borderTopColor: \"rgb(239, 68, 68)\",\r\n          }}\r\n        />\r\n      </div>\r\n      {text && (\r\n        <h2 className=\"text-lg text-center font-medium text-gray-600\">\r\n          {text}\r\n        </h2>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,WAAW,EAAE,OAAO,EAAE,EAAE,IAAI,EAAmB;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBAClB;;;;;;;;;;;;YAGH,sBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;;;;;;;AAKX;KA5BgB"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/head-wrapper.tsx"], "sourcesContent": ["import Head from \"next/head\";\r\n\r\nexport default function HeadWrapper({\r\n  title,\r\n  description,\r\n}: {\r\n  title: string;\r\n  description?: string;\r\n}) {\r\n  return (\r\n    <Head>\r\n      <title>{`${title || \"\"} | Student's Portal | AdmitPath`}</title>\r\n      <meta\r\n        name=\"description\"\r\n        content={`Student Dashboard Portal for accessing and managing all your sessions and resources. ${\r\n          description || \"\"\r\n        } | AdmitPath`}\r\n      />\r\n    </Head>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,YAAY,EAClC,KAAK,EACL,WAAW,EAIZ;IACC,qBACE,6LAAC,uKAAA,CAAA,UAAI;;0BACH,6LAAC;0BAAO,GAAG,SAAS,GAAG,+BAA+B,CAAC;;;;;;0BACvD,6LAAC;gBACC,MAAK;gBACL,SAAS,CAAC,qFAAqF,EAC7F,eAAe,GAChB,YAAY,CAAC;;;;;;;;;;;;AAItB;KAlBwB"}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/ai-counsellor/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  Send,\r\n  Loader2,\r\n  Play,\r\n  AlertCircle,\r\n  MessageCircle,\r\n  Video,\r\n} from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"@/app/components/ui/button\";\r\nimport { Input } from \"@/app/components/ui/input\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/app/components/ui/card\";\r\nimport { Alert, AlertDescription } from \"@/app/components/ui/alert\";\r\nimport { LogoLoader } from \"@/app/components/ui/logo-loader\";\r\nimport HeadWrapper from \"@/app/components/student/head-wrapper\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface VideoState {\r\n  isGenerating: boolean;\r\n  videoId: string | null;\r\n  videoUrl: string | null;\r\n  error: string | null;\r\n}\r\n\r\nexport default function AICounsellorPage() {\r\n  const router = useRouter();\r\n  const [mode, setMode] = useState<\"select\" | \"video\">(\"select\");\r\n  const [query, setQuery] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [aiResponse, setAiResponse] = useState<string | null>(null);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [videoState, setVideoState] = useState<VideoState>({\r\n    isGenerating: false,\r\n    videoId: null,\r\n    videoUrl: null,\r\n    error: null,\r\n  });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!query.trim()) {\r\n      setError(\"Please enter a question or query\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    setAiResponse(null);\r\n    setVideoState({\r\n      isGenerating: false,\r\n      videoId: null,\r\n      videoUrl: null,\r\n      error: null,\r\n    });\r\n\r\n    try {\r\n      const aiRes = await fetch(\"/api/ai-counsellor/generate-response\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({ query }),\r\n      });\r\n\r\n      const aiData = await aiRes.json();\r\n\r\n      if (!aiData.success) {\r\n        throw new Error(aiData.error || \"Failed to generate AI response\");\r\n      }\r\n\r\n      setAiResponse(aiData.response);\r\n      setIsLoading(false);\r\n\r\n      setVideoState((prev) => ({ ...prev, isGenerating: true }));\r\n\r\n      const videoRes = await fetch(\"/api/ai-counsellor/generate-video\", {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({ text: aiData.response }),\r\n      });\r\n\r\n      const videoData = await videoRes.json();\r\n\r\n      if (!videoData.success) {\r\n        throw new Error(videoData.error || \"Failed to generate video\");\r\n      }\r\n\r\n      setVideoState((prev) => ({\r\n        ...prev,\r\n        videoId: videoData.video_id,\r\n        progress: 10,\r\n      }));\r\n\r\n      pollVideoStatus(videoData.video_id);\r\n    } catch (err: any) {\r\n      setError(err.message || \"An error occurred\");\r\n      setIsLoading(false);\r\n      setVideoState((prev) => ({\r\n        ...prev,\r\n        isGenerating: false,\r\n        error: err.message,\r\n      }));\r\n    }\r\n  };\r\n\r\n  const pollVideoStatus = async (videoId: string) => {\r\n    const maxAttempts = 60; // 5 minutes max (5 second intervals)\r\n    let attempts = 0;\r\n\r\n    const poll = async () => {\r\n      try {\r\n        const response = await fetch(\r\n          `/api/ai-counsellor/video-status?video_id=${videoId}`\r\n        );\r\n        const data = await response.json();\r\n\r\n        if (!data.success) {\r\n          throw new Error(data.error || \"Failed to check video status\");\r\n        }\r\n\r\n        const progress = Math.min(20 + attempts * 2, 90);\r\n        setVideoState((prev) => ({ ...prev, progress }));\r\n\r\n        if (data.status === \"completed\" && data.video_url) {\r\n          setVideoState((prev) => ({\r\n            ...prev,\r\n            isGenerating: false,\r\n            videoUrl: data.video_url,\r\n            progress: 100,\r\n          }));\r\n          return;\r\n        }\r\n\r\n        if (data.status === \"failed\") {\r\n          throw new Error(\"Video generation failed\");\r\n        }\r\n\r\n        attempts++;\r\n        if (attempts < maxAttempts) {\r\n          setTimeout(poll, 5000); // Poll every 5 seconds\r\n        } else {\r\n          throw new Error(\"Video generation timed out\");\r\n        }\r\n      } catch (err: any) {\r\n        setVideoState((prev) => ({\r\n          ...prev,\r\n          isGenerating: false,\r\n          error: err.message,\r\n        }));\r\n      }\r\n    };\r\n\r\n    poll();\r\n  };\r\n\r\n  const resetSession = () => {\r\n    setQuery(\"\");\r\n    setAiResponse(null);\r\n    setError(null);\r\n    setVideoState({\r\n      isGenerating: false,\r\n      videoId: null,\r\n      videoUrl: null,\r\n      error: null,\r\n    });\r\n  };\r\n\r\n  // Show selection screen first\r\n  if (mode === \"select\") {\r\n    return (\r\n      <>\r\n        <HeadWrapper title=\"AI Counsellor\" />\r\n        <div className=\"p-4 md:p-6\">\r\n          <div className=\"max-w-4xl mx-auto space-y-6\">\r\n            {/* Header */}\r\n            <Card className=\"border-none bg-white\">\r\n              <CardHeader className=\"text-center pb-4\">\r\n                <div className=\"flex items-center justify-center gap-3 mb-2\">\r\n                  <div className=\"p-3 bg-blue-100 rounded-full\">\r\n                    <Bot className=\"w-8 h-8 text-blue-600\" />\r\n                  </div>\r\n                  <CardTitle className=\"text-2xl md:text-3xl font-bold text-gray-800\">\r\n                    AI Counsellor\r\n                  </CardTitle>\r\n                </div>\r\n                <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n                  Choose how you'd like to interact with our AI counsellor for\r\n                  personalized guidance and advice.\r\n                </p>\r\n              </CardHeader>\r\n            </Card>\r\n\r\n            {/* Options */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Video Counselling Option */}\r\n              <Card\r\n                className=\"border-none shadow-md bg-white hover:shadow-lg transition-shadow cursor-pointer\"\r\n                onClick={() => setMode(\"video\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center flex flex-col justify-between h-full\">\r\n                  <div>\r\n                    <div className=\"flex items-center justify-center gap-3 mb-4\">\r\n                      <div className=\"p-4 bg-purple-100 rounded-full\">\r\n                        <Video className=\"w-8 h-8 text-purple-600\" />\r\n                      </div>\r\n                    </div>\r\n                    <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\r\n                      Video Counselling\r\n                    </h3>\r\n                  </div>\r\n                  <p className=\"text-gray-600 mb-4\">\r\n                    Ask questions and receive comprehensive video responses with\r\n                    personalized guidance from our AI counsellor.\r\n                  </p>\r\n                  <Button>Start Video Session</Button>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Chat Option */}\r\n              <Card\r\n                className=\"border-none shadow-md bg-white hover:shadow-lg transition-shadow cursor-pointer\"\r\n                onClick={() =>\r\n                  router.push(\"/student/dashboard/ai-counsellor/chat\")\r\n                }\r\n              >\r\n                <CardContent className=\"p-6 text-center flex flex-col justify-between h-full\">\r\n                  <div>\r\n                    <div className=\"flex items-center justify-center gap-3 mb-4\">\r\n                      <div className=\"p-4 bg-green-100 rounded-full\">\r\n                        <MessageCircle className=\"w-8 h-8 text-green-600\" />\r\n                      </div>\r\n                    </div>\r\n                    <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\r\n                      Chat with AI\r\n                    </h3>\r\n                  </div>\r\n                  <p className=\"text-gray-600 mb-4\">\r\n                    Have a real-time text conversation with our AI counsellor\r\n                    for quick advice and guidance.\r\n                  </p>\r\n                  <Button>Start Chat</Button>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n\r\n  // Video counselling mode (existing functionality)\r\n  return (\r\n    <>\r\n      <HeadWrapper title=\"AI Counsellor - Video Session\" />\r\n      <div className=\"p-4 md:p-6\">\r\n        <div className=\"max-w-4xl mx-auto space-y-6\">\r\n          {/* Header with back button */}\r\n          <Card className=\"border-none shadow-md bg-white\">\r\n            <CardHeader className=\"text-center pb-4\">\r\n              <div className=\"flex items-center justify-center mb-2\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"p-3 bg-purple-100 rounded-full\">\r\n                    <Video className=\"w-8 h-8 text-purple-600\" />\r\n                  </div>\r\n                  <CardTitle className=\"text-2xl md:text-3xl font-bold text-gray-800\">\r\n                    Video Counselling\r\n                  </CardTitle>\r\n                </div>\r\n                <div></div> {/* Spacer for centering */}\r\n              </div>\r\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n                Ask any questions about university admissions, career planning,\r\n                or academic guidance, and receive a comprehensive video\r\n                response.\r\n              </p>\r\n            </CardHeader>\r\n          </Card>\r\n\r\n          {/* Input Form */}\r\n          <Card className=\"border-none shadow-md bg-white\">\r\n            <CardContent className=\"p-6\">\r\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                <div className=\"space-y-2\">\r\n                  <label\r\n                    htmlFor=\"query\"\r\n                    className=\"text-sm font-medium text-gray-700\"\r\n                  >\r\n                    What would you like to know?\r\n                  </label>\r\n                  <div className=\"flex gap-2\">\r\n                    <Input\r\n                      id=\"query\"\r\n                      value={query}\r\n                      onChange={(e) => setQuery(e.target.value)}\r\n                      placeholder=\"Ask about university admissions, career guidance, study tips...\"\r\n                      className=\"flex-1 h-12 text-base\"\r\n                      disabled={isLoading || videoState.isGenerating}\r\n                      maxLength={1000}\r\n                    />\r\n                    <Button\r\n                      type=\"submit\"\r\n                      disabled={\r\n                        isLoading || videoState.isGenerating || !query.trim()\r\n                      }\r\n                      className=\"h-12 px-6 bg-blue-600 hover:bg-blue-700\"\r\n                    >\r\n                      {isLoading ? (\r\n                        <Loader2 className=\"w-5 h-5 animate-spin\" />\r\n                      ) : (\r\n                        <Send className=\"w-5 h-5\" />\r\n                      )}\r\n                    </Button>\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-500\">\r\n                    {query.length}/1000 characters\r\n                  </p>\r\n                </div>\r\n              </form>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Error Display */}\r\n          {error && (\r\n            <Alert className=\"border-red-200 bg-red-50\">\r\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\r\n              <AlertDescription className=\"text-red-800\">\r\n                {error}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n\r\n          {/* Loading State */}\r\n          {(isLoading || videoState.isGenerating) && (\r\n            <Card className=\"border-none shadow-md bg-white\">\r\n              <CardContent className=\"p-8 text-center\">\r\n                <LogoLoader\r\n                  size={60}\r\n                  text={isLoading ? \"Generating Response...\" : \"Generating...\"}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Video Result */}\r\n          {videoState.videoUrl && (\r\n            <Card className=\"border-none shadow-md bg-white\">\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2\">\r\n                  <Play className=\"w-5 h-5 text-green-600\" />\r\n                  Your AI Counsellor Response\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"aspect-video bg-black rounded-lg overflow-hidden\">\r\n                  <video\r\n                    controls\r\n                    className=\"w-full h-full\"\r\n                    src={videoState.videoUrl}\r\n                    poster=\"/images/video-placeholder.jpg\"\r\n                  >\r\n                    Your browser does not support the video tag.\r\n                  </video>\r\n                </div>\r\n                <div className=\"flex justify-center\">\r\n                  <Button\r\n                    onClick={resetSession}\r\n                    variant=\"outline\"\r\n                    className=\"px-6\"\r\n                  >\r\n                    Ask Another Question\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Video Error */}\r\n          {videoState.error && (\r\n            <Alert className=\"border-red-200 bg-red-50\">\r\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\r\n              <AlertDescription className=\"text-red-800\">\r\n                Video generation failed: {videoState.error}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAUA;AACA;AACA;AAMA;AACA;AACA;AACA;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,cAAc;QACd,SAAS;QACT,UAAU;QACV,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QACT,cAAc;QACd,cAAc;YACZ,cAAc;YACd,SAAS;YACT,UAAU;YACV,OAAO;QACT;QAEA,IAAI;YACF,MAAM,QAAQ,MAAM,MAAM,wCAAwC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,SAAS,MAAM,MAAM,IAAI;YAE/B,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,cAAc,OAAO,QAAQ;YAC7B,aAAa;YAEb,cAAc,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAK,CAAC;YAExD,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM,OAAO,QAAQ;gBAAC;YAC/C;YAEA,MAAM,YAAY,MAAM,SAAS,IAAI;YAErC,IAAI,CAAC,UAAU,OAAO,EAAE;gBACtB,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,cAAc,CAAC,OAAS,CAAC;oBACvB,GAAG,IAAI;oBACP,SAAS,UAAU,QAAQ;oBAC3B,UAAU;gBACZ,CAAC;YAED,gBAAgB,UAAU,QAAQ;QACpC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,aAAa;YACb,cAAc,CAAC,OAAS,CAAC;oBACvB,GAAG,IAAI;oBACP,cAAc;oBACd,OAAO,IAAI,OAAO;gBACpB,CAAC;QACH;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,cAAc,IAAI,qCAAqC;QAC7D,IAAI,WAAW;QAEf,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,yCAAyC,EAAE,SAAS;gBAEvD,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,WAAW,GAAG;gBAC7C,cAAc,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAS,CAAC;gBAE9C,IAAI,KAAK,MAAM,KAAK,eAAe,KAAK,SAAS,EAAE;oBACjD,cAAc,CAAC,OAAS,CAAC;4BACvB,GAAG,IAAI;4BACP,cAAc;4BACd,UAAU,KAAK,SAAS;4BACxB,UAAU;wBACZ,CAAC;oBACD;gBACF;gBAEA,IAAI,KAAK,MAAM,KAAK,UAAU;oBAC5B,MAAM,IAAI,MAAM;gBAClB;gBAEA;gBACA,IAAI,WAAW,aAAa;oBAC1B,WAAW,MAAM,OAAO,uBAAuB;gBACjD,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,KAAU;gBACjB,cAAc,CAAC,OAAS,CAAC;wBACvB,GAAG,IAAI;wBACP,cAAc;wBACd,OAAO,IAAI,OAAO;oBACpB,CAAC;YACH;QACF;QAEA;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,cAAc;QACd,SAAS;QACT,cAAc;YACZ,cAAc;YACd,SAAS;YACT,UAAU;YACV,OAAO;QACT;IACF;IAEA,8BAA8B;IAC9B,IAAI,SAAS,UAAU;QACrB,qBACE;;8BACE,6LAAC,mJAAA,CAAA,UAAW;oBAAC,OAAM;;;;;;8BACnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA+C;;;;;;;;;;;;sDAItE,6LAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;;;;;;0CAQnD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;wCACH,WAAU;wCACV,SAAS,IAAM,QAAQ;kDAEvB,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGrB,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;;;;;;;8DAI3D,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAIlC,6LAAC,qIAAA,CAAA,SAAM;8DAAC;;;;;;;;;;;;;;;;;kDAKZ,6LAAC,mIAAA,CAAA,OAAI;wCACH,WAAU;wCACV,SAAS,IACP,OAAO,IAAI,CAAC;kDAGd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG7B,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;;;;;;;8DAI3D,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAIlC,6LAAC,qIAAA,CAAA,SAAM;8DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxB;IAEA,kDAAkD;IAClD,qBACE;;0BACE,6LAAC,mJAAA,CAAA,UAAW;gBAAC,OAAM;;;;;;0BACnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAA+C;;;;;;;;;;;;0DAItE,6LAAC;;;;;4CAAU;;;;;;;kDAEb,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCASnD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;8CACtC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,aAAY;wDACZ,WAAU;wDACV,UAAU,aAAa,WAAW,YAAY;wDAC9C,WAAW;;;;;;kEAEb,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UACE,aAAa,WAAW,YAAY,IAAI,CAAC,MAAM,IAAI;wDAErD,WAAU;kEAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAItB,6LAAC;gDAAE,WAAU;;oDACV,MAAM,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQvB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;wBAMN,CAAC,aAAa,WAAW,YAAY,mBACpC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,6IAAA,CAAA,aAAU;oCACT,MAAM;oCACN,MAAM,YAAY,2BAA2B;;;;;;;;;;;;;;;;wBAOpD,WAAW,QAAQ,kBAClB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA2B;;;;;;;;;;;;8CAI/C,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,QAAQ;gDACR,WAAU;gDACV,KAAK,WAAW,QAAQ;gDACxB,QAAO;0DACR;;;;;;;;;;;sDAIH,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;wBASR,WAAW,KAAK,kBACf,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;wCAAe;wCACf,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GAhXwB;;QACP,qIAAA,CAAA,YAAS;;;KADF"}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,wIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/next/src/client/components/noop-head.tsx"], "sourcesContent": ["export default function NoopHead() {\n  return null\n}\n"], "names": ["NoopHead"], "mappings": ";;;;+BAAA,WAAA;;;eAAwBA;;;AAAT,SAASA;IACtB,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "file": "video.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/video.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTMgNS4yMjMgMy40ODJhLjUuNSAwIDAgMCAuNzc3LS40MTZWNy44N2EuNS41IDAgMCAwLS43NTItLjQzMkwxNiAxMC41IiAvPgogIDxyZWN0IHg9IjIiIHk9IjYiIHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('Video', [\n  [\n    'path',\n    {\n      d: 'm16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5',\n      key: 'ftymec',\n    },\n  ],\n  ['rect', { x: '2', y: '6', width: '14', height: '12', rx: '2', key: '158x01' }],\n]);\n\nexport default Video;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAQ,UAAA,EAAiB,OAAS,CAAA,CAAA,CAAA;IACtC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC/E,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "file": "send.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/send.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Send\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNTM2IDIxLjY4NmEuNS41IDAgMCAwIC45MzctLjAyNGw2LjUtMTlhLjQ5Ni40OTYgMCAwIDAtLjYzNS0uNjM1bC0xOSA2LjVhLjUuNSAwIDAgMC0uMDI0LjkzN2w3LjkzIDMuMThhMiAyIDAgMCAxIDEuMTEyIDEuMTF6IiAvPgogIDxwYXRoIGQ9Im0yMS44NTQgMi4xNDctMTAuOTQgMTAuOTM5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/send\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Send = createLucideIcon('Send', [\n  [\n    'path',\n    {\n      d: 'M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z',\n      key: '1ffxy3',\n    },\n  ],\n  ['path', { d: 'm21.854 2.147-10.94 10.939', key: '12cjpa' }],\n]);\n\nexport default Send;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAO,UAAA,EAAiB,MAAQ,CAAA,CAAA,CAAA;IACpC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1348, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', [\n  ['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }],\n]);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAe,UAAA,EAAiB,cAAgB,CAAA,CAAA,CAAA;IACpD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('CircleAlert', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n]);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAc,UAAA,EAAiB,aAAe,CAAA,CAAA,CAAA;IAClD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACtE,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', [\n  ['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }],\n]);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAO,UAAA,EAAiB,MAAQ,CAAA,CAAA,CAAA;IACpC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "file": "message-circle.js", "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('MessageCircle', [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n]);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAgB,UAAA,EAAiB,eAAiB,CAAA,CAAA,CAAA;IACtD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChE,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}