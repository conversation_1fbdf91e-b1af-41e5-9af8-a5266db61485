{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/time-helpers.ts"], "sourcesContent": ["// Convert date to ISO format\r\nexport const formatDateISO = (date: string) => {\r\n  // Create a date object from the date string\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the time to midnight (00:00:00) to get just the date\r\n  dateObj.setHours(0, 0, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n};\r\n\r\n// Convert to long date string\r\nexport function formatToLongDateString(dateString: string) {\r\n  const date = new Date(dateString);\r\n\r\n  return date.toLocaleDateString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n  });\r\n}\r\n\r\n// Convert date to 'yyyy-mm-dd' format in LOCAL TIMEZONE\r\nexport const formatNormalDate = (date: string) => {\r\n  const d = new Date(date);\r\n  const year = d.getFullYear();\r\n  const month = String(d.getMonth() + 1).padStart(2, \"0\"); // Months are 0-based\r\n  const day = String(d.getDate()).padStart(2, \"0\");\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// Convert ISO time to \"HH:mm\" in LOCAL TIMEZONE\r\nexport function formatTimeForInput(isoTime: string) {\r\n  const date = new Date(isoTime);\r\n  const hours = String(date.getHours()).padStart(2, \"0\"); // Local hours\r\n  const minutes = String(date.getMinutes()).padStart(2, \"0\"); // Local minutes\r\n  return `${hours}:${minutes}`;\r\n}\r\n\r\n// Convert \"HH:mm\" from time input back to ISO format\r\nexport function formatTimeToISO(date: string, time: string) {\r\n  // Create a date object from the date and time strings\r\n  const [hours, minutes] = time.split(':').map(Number);\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the hours and minutes\r\n  dateObj.setHours(hours, minutes, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n}\r\n\r\nexport function dateNowWithTZOffset() {\r\n  const now = new Date();\r\n\r\n  const year = now.getFullYear();\r\n  const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n  const day = String(now.getDate()).padStart(2, \"0\");\r\n  const hours = String(now.getHours()).padStart(2, \"0\");\r\n  const minutes = String(now.getMinutes()).padStart(2, \"0\");\r\n  const seconds = String(now.getSeconds()).padStart(2, \"0\");\r\n\r\n  const microseconds = String(Math.floor(Math.random() * 1000000)).padStart(\r\n    6,\r\n    \"0\"\r\n  );\r\n\r\n  const tzOffset = now.getTimezoneOffset();\r\n  const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60));\r\n  const tzOffsetMinutes = Math.abs(tzOffset % 60);\r\n  const tzOffsetSign = tzOffset <= 0 ? \"+\" : \"-\";\r\n\r\n  const tzFormatted = `${tzOffsetSign}${String(tzOffsetHours).padStart(\r\n    2,\r\n    \"0\"\r\n  )}:${String(tzOffsetMinutes).padStart(2, \"0\")}`;\r\n\r\n  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}${tzFormatted}`;\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;;AACtB,MAAM,gBAAgB,CAAC;IAC5B,4CAA4C;IAC5C,MAAM,UAAU,IAAI,KAAK;IAEzB,2DAA2D;IAC3D,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG;IAE1B,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAGO,SAAS,uBAAuB,UAAkB;IACvD,MAAM,OAAO,IAAI,KAAK;IAEtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,OAAO,EAAE,WAAW;IAC1B,MAAM,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,qBAAqB;IAC9E,MAAM,MAAM,OAAO,EAAE,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC5C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AAClC;AAGO,SAAS,mBAAmB,OAAe;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,MAAM,cAAc;IACtE,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,MAAM,gBAAgB;IAC5E,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAGO,SAAS,gBAAgB,IAAY,EAAE,IAAY;IACxD,sDAAsD;IACtD,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;IAC7C,MAAM,UAAU,IAAI,KAAK;IAEzB,4BAA4B;IAC5B,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;IAEpC,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAEO,SAAS;IACd,MAAM,MAAM,IAAI;IAEhB,MAAM,OAAO,IAAI,WAAW;IAC5B,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,MAAM,OAAO,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC9C,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG;IACjD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IAErD,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CACvE,GACA;IAGF,MAAM,WAAW,IAAI,iBAAiB;IACtC,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW;IACrD,MAAM,kBAAkB,KAAK,GAAG,CAAC,WAAW;IAC5C,MAAM,eAAe,YAAY,IAAI,MAAM;IAE3C,MAAM,cAAc,GAAG,eAAe,OAAO,eAAe,QAAQ,CAClE,GACA,KACA,CAAC,EAAE,OAAO,iBAAiB,QAAQ,CAAC,GAAG,MAAM;IAE/C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,eAAe,aAAa;AAC/F"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useWebSocketChat.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  ChatState,\r\n  Message,\r\n  SendMessagePayload,\r\n  InitialMessagePayload,\r\n  WebSocketMessage,\r\n  WebSocketConnectionStatus,\r\n} from \"@/app/types/chat\";\r\nimport { dateNowWithTZOffset } from \"../utils/time-helpers\";\r\n\r\nconst LOCALSTORAGE_KEY = \"chat_latest_messages\";\r\n\r\ninterface LatestMessagesStorage {\r\n  [channelId: string]: string;\r\n}\r\n\r\nclass WebSocketManager {\r\n  private socket: WebSocket | null = null;\r\n  private channelId: string | null = null;\r\n  private token: string | null = null;\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private reconnectInterval = 3000; // 3 seconds\r\n  private reconnectTimeoutId: NodeJS.Timeout | null = null;\r\n  private messageHandler: ((data: any) => void) | null = null;\r\n  private statusChangeHandler: ((status: WebSocketConnectionStatus) => void) | null = null;\r\n  private errorHandler: ((error: any) => void) | null = null;\r\n\r\n  constructor() {\r\n    this.connect = this.connect.bind(this);\r\n    this.disconnect = this.disconnect.bind(this);\r\n    this.sendMessage = this.sendMessage.bind(this);\r\n    this.handleOpen = this.handleOpen.bind(this);\r\n    this.handleMessage = this.handleMessage.bind(this);\r\n    this.handleClose = this.handleClose.bind(this);\r\n    this.handleError = this.handleError.bind(this);\r\n    this.reconnect = this.reconnect.bind(this);\r\n  }\r\n\r\n  public connect(channelId: string, token: string): void {\r\n    this.channelId = channelId;\r\n    this.token = token;\r\n\r\n    if (this.socket) {\r\n      this.socket.close();\r\n    }\r\n\r\n    try {\r\n      const wsUrl = `${this.getWebSocketBaseUrl()}/ws/chat/${channelId}?token=${token}`;\r\n      this.socket = new WebSocket(wsUrl);\r\n\r\n      this.socket.onopen = this.handleOpen;\r\n      this.socket.onmessage = this.handleMessage;\r\n      this.socket.onclose = this.handleClose;\r\n      this.socket.onerror = this.handleError;\r\n\r\n      if (this.statusChangeHandler) {\r\n        this.statusChangeHandler('connecting');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"WebSocket connection error:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n    }\r\n  }\r\n\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      this.socket = null;\r\n    }\r\n\r\n    if (this.reconnectTimeoutId) {\r\n      clearTimeout(this.reconnectTimeoutId);\r\n      this.reconnectTimeoutId = null;\r\n    }\r\n\r\n    this.channelId = null;\r\n    this.token = null;\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n  }\r\n\r\n  public sendMessage(message: WebSocketMessage): boolean {\r\n    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {\r\n      console.error(\"WebSocket is not connected\");\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      this.socket.send(JSON.stringify(message));\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error sending message:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n      return false;\r\n    }\r\n  }\r\n\r\n  public onMessage(handler: (data: any) => void): void {\r\n    this.messageHandler = handler;\r\n  }\r\n\r\n  public onStatusChange(handler: (status: WebSocketConnectionStatus) => void): void {\r\n    this.statusChangeHandler = handler;\r\n  }\r\n\r\n  public onError(handler: (error: any) => void): void {\r\n    this.errorHandler = handler;\r\n  }\r\n\r\n  public isConnected(): boolean {\r\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\r\n  }\r\n\r\n  private handleOpen(_event: Event): void {\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('connected');\r\n    }\r\n  }\r\n\r\n  private handleMessage(event: MessageEvent): void {\r\n    try {\r\n      const data = JSON.parse(event.data);\r\n\r\n      const now = new Date();\r\n      data._receivedAt = now.toISOString();\r\n\r\n      if (!this._recentMessages) {\r\n        this._recentMessages = [];\r\n      }\r\n\r\n      const fingerprint = `${data.sender_id || ''}-${data.created_at || ''}-${(data.message || '').substring(0, 20)}`;\r\n\r\n      // Check if we've seen this message recently (within 5 seconds)\r\n      const isDuplicate = this._recentMessages.some(m => {\r\n        // If fingerprints match and received within 5 seconds, it's likely a duplicate\r\n        if (m.fingerprint === fingerprint) {\r\n          const prevTime = new Date(m.receivedAt).getTime();\r\n          const currentTime = now.getTime();\r\n          return (currentTime - prevTime) < 5000; // 5 seconds\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      this._recentMessages.push({\r\n        fingerprint,\r\n        receivedAt: now.toISOString()\r\n      });\r\n\r\n      if (this._recentMessages.length > 20) {\r\n        this._recentMessages = this._recentMessages.slice(-20);\r\n      }\r\n\r\n      if (this.messageHandler) {\r\n        this.messageHandler(data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error parsing message:\", error);\r\n    }\r\n  }\r\n\r\n  private _recentMessages: Array<{fingerprint: string, receivedAt: string}> = [];\r\n\r\n  private handleClose(event: CloseEvent): void {\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n\r\n    if (!event.wasClean && this.channelId && this.token) {\r\n      this.reconnect();\r\n    }\r\n  }\r\n\r\n  private handleError(event: Event): void {\r\n    console.error(\"WebSocket error:\", event);\r\n    if (this.errorHandler) {\r\n      this.errorHandler(event);\r\n    }\r\n  }\r\n\r\n  private reconnect(): void {\r\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n      return;\r\n    }\r\n\r\n    this.reconnectAttempts++;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('reconnecting');\r\n    }\r\n\r\n    this.reconnectTimeoutId = setTimeout(() => {\r\n      if (this.channelId && this.token) {\r\n        this.connect(this.channelId, this.token);\r\n      }\r\n    }, this.reconnectInterval);\r\n  }\r\n\r\n  private getWebSocketBaseUrl(): string {\r\n    const isSecure = window.location.protocol === 'https:';\r\n    const protocol = isSecure ? 'wss:' : 'ws:';\r\n\r\n    let apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n\r\n    apiBaseUrl = apiBaseUrl.replace(/^https?:\\/\\//, '');\r\n\r\n    return `${protocol}//${apiBaseUrl}`;\r\n  }\r\n}\r\n\r\nconst wsManager = new WebSocketManager();\r\n\r\nlet currentUserInfo: { user_id: number | string } | null = null;\r\n\r\nexport const setWebSocketChatUserInfo = (userInfo: { user_id: number | string }) => {\r\n  currentUserInfo = userInfo;\r\n};\r\n\r\nexport const useWebSocketChat = create<ChatState>((set, get) => ({\r\n  channels: null,\r\n  currentChannel: null,\r\n  messages: null,\r\n  messageIds: new Set<number>(),\r\n  pendingMessages: new Set<number>(),\r\n  unseenMessages: {},\r\n  unseenChannelsCount: 0,\r\n  loading: false,\r\n  error: null,\r\n  connectionStatus: 'disconnected',\r\n\r\n  initWebSocket: () => {\r\n    wsManager.onMessage((data) => {\r\n      const message = data as Message;\r\n      const state = get();\r\n\r\n\r\n      if (currentUserInfo?.user_id && message.sender_id === currentUserInfo.user_id) {\r\n        return;\r\n      }\r\n\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return;\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return;\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const isDuplicate = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      get().addMessage(message);\r\n\r\n      if (state.currentChannel) {\r\n        get().updateLatestSeenMessage(\r\n          state.currentChannel.channel_id,\r\n          message.created_at\r\n        );\r\n      }\r\n    });\r\n\r\n    wsManager.onStatusChange((status) => {\r\n      set({ connectionStatus: status });\r\n    });\r\n\r\n    wsManager.onError((_error) => {\r\n      set({ error: \"WebSocket connection error\" });\r\n    });\r\n  },\r\n\r\n  fetchChannels: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/messages/channels\");\r\n      set({ channels: response.data.channels, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch channels\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchMessages: async (channelId: string, skip = 0, limit = 15) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/messages/channel/${channelId}?skip=${skip}&limit=${limit}`\r\n      );\r\n      const newMessages = response.data.messages;\r\n\r\n      const messageIds = new Set(get().messageIds);\r\n      newMessages.forEach((msg: Message) => messageIds.add(msg.id));\r\n\r\n      set((state) => ({\r\n        messages:\r\n          skip === 0\r\n            ? newMessages\r\n            : [\r\n                ...(state.messages || []),\r\n                ...newMessages.filter(\r\n                  (msg: Message) =>\r\n                    !state.messages?.some(\r\n                      (existingMsg: Message) => existingMsg.id === msg.id\r\n                    )\r\n                ),\r\n              ],\r\n        messageIds,\r\n        loading: false,\r\n      }));\r\n\r\n      if (limit !== 1 && newMessages.length > 0) {\r\n        get().updateLatestSeenMessage(channelId, newMessages[0].created_at);\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch messages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  sendMessage: async (payload: SendMessagePayload) => {\r\n    const currentChannel = get().currentChannel;\r\n    if (!currentChannel) return;\r\n\r\n    const state = get();\r\n    const messageFingerprint = `${payload.sender_id}-${dateNowWithTZOffset()}-${payload.message.substring(0, 20)}`;\r\n\r\n    const isDuplicate = state.messages?.some(m => {\r\n      if (m.sender_id !== payload.sender_id) return false;\r\n\r\n      if (m.message !== payload.message) return false;\r\n\r\n      const msgTime = new Date(m.created_at).getTime();\r\n      const now = new Date().getTime();\r\n      return (now - msgTime) < 2000; // 2 seconds\r\n    });\r\n\r\n    if (isDuplicate) {\r\n      return;\r\n    }\r\n\r\n    // Create optimistic message\r\n    const tempId = Math.floor(Math.random() * 1000000);\r\n    const optimisticMessage: Message = {\r\n      id: tempId,\r\n      channel_id: currentChannel.channel_id,\r\n      message: payload.message,\r\n      sender_id: payload.sender_id,\r\n      sender_name: payload.sender_name,\r\n      created_at: dateNowWithTZOffset(),\r\n    };\r\n\r\n    try {\r\n      get().addMessage(optimisticMessage);\r\n      set((state) => ({\r\n        pendingMessages: new Set([...state.pendingMessages, tempId]),\r\n      }));\r\n\r\n      let serverMessage: Message | null = null;\r\n\r\n      if (wsManager.isConnected()) {\r\n        const wsMessage: WebSocketMessage = {\r\n          type: \"message\",\r\n          message: payload.message,\r\n          recipient_id: payload.recipient_id,\r\n          created_at: optimisticMessage.created_at,\r\n        };\r\n\r\n        const sentViaWebSocket = wsManager.sendMessage(wsMessage);\r\n\r\n        if (sentViaWebSocket) {\r\n\r\n\r\n          set((state) => ({\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          }));\r\n\r\n          return;\r\n        }\r\n      }\r\n\r\n      const response = await apiClient.post(`/messages`, {\r\n        recipient_id: payload.recipient_id,\r\n        message: payload.message,\r\n        created_at: optimisticMessage.created_at,\r\n      });\r\n\r\n      serverMessage = response.data;\r\n\r\n      if (serverMessage) {\r\n        get().updateLatestSeenMessage(\r\n          currentChannel.channel_id,\r\n          serverMessage.created_at\r\n        );\r\n      }\r\n\r\n      set((state) => {\r\n        const serverMessageExists = serverMessage && state.messages?.some(msg => msg.id === serverMessage.id);\r\n\r\n        const serverMessageFingerprint = serverMessage ?\r\n          `${serverMessage.sender_id}-${serverMessage.created_at}-${serverMessage.message.substring(0, 20)}` : '';\r\n\r\n        const similarMessageExists = serverMessage && state.messages?.some(msg => {\r\n          if (msg.id === tempId) return false; // Skip the optimistic message\r\n          const msgFingerprint = `${msg.sender_id}-${msg.created_at}-${msg.message.substring(0, 20)}`;\r\n          return msgFingerprint === serverMessageFingerprint;\r\n        });\r\n\r\n        if (serverMessageExists || similarMessageExists) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        if (!serverMessage) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        return {\r\n          messages: (state.messages || []).map((msg) =>\r\n            msg.id === tempId ? serverMessage : msg\r\n          ),\r\n          messageIds: new Set(\r\n            [...(state.messageIds || [])]\r\n              .filter((id) => id !== tempId)\r\n              .concat(serverMessage.id)\r\n          ),\r\n          pendingMessages: new Set(\r\n            [...state.pendingMessages].filter((id) => id !== tempId)\r\n          ),\r\n        };\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to send message:\", error);\r\n      get().removeMessage(tempId);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  sendInitialMessage: async (payload: InitialMessagePayload) => {\r\n    try {\r\n      await apiClient.post(`/messages`, {\r\n        ...payload,\r\n        created_at: dateNowWithTZOffset(),\r\n      });\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  setCurrentChannel: (channel) => {\r\n    const currentChannelId = get().currentChannel?.channel_id;\r\n\r\n    set({\r\n      messages: null,\r\n      messageIds: new Set<number>()\r\n    });\r\n\r\n    if (currentChannelId) {\r\n      wsManager.disconnect();\r\n    }\r\n\r\n    set({ currentChannel: channel });\r\n\r\n    if (channel) {\r\n\r\n      const token = localStorage.getItem(\"access_token\");\r\n\r\n      if (token) {\r\n        get().initWebSocket();\r\n\r\n        wsManager.connect(channel.channel_id, token);\r\n\r\n        get().fetchMessages(channel.channel_id);\r\n      } else {\r\n        set({ error: \"Authentication token not found\" });\r\n      }\r\n    }\r\n  },\r\n\r\n  addMessage: (message: Message) => {\r\n    set((state) => {\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const messageExistsByFingerprint = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (messageExistsByFingerprint) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      return {\r\n        messages: [...(state.messages || []), message],\r\n        messageIds: message.id ? new Set(state.messageIds).add(message.id) : state.messageIds,\r\n      };\r\n    });\r\n  },\r\n\r\n  removeMessage: (messageId: number) => {\r\n    set((state) => ({\r\n      messages: (state.messages || []).filter((msg) => msg.id !== messageId),\r\n      messageIds: new Set(\r\n        [...state.messageIds].filter((id) => id !== messageId)\r\n      ),\r\n      pendingMessages: new Set(\r\n        [...state.pendingMessages].filter((id) => id !== messageId)\r\n      ),\r\n    }));\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n\r\n  updateLatestSeenMessage: (channelId: string, timestamp: string) => {\r\n    try {\r\n      let latestMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (latestMessages[channelId] === timestamp) return;\r\n\r\n      latestMessages[channelId] = timestamp;\r\n\r\n      localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(latestMessages));\r\n\r\n      set((state) => ({\r\n        unseenMessages: {\r\n          ...state.unseenMessages,\r\n          [channelId]: 0,\r\n        },\r\n        unseenChannelsCount: Math.max(0, state.unseenChannelsCount - 1),\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Failed to update latest seen message:\", error);\r\n    }\r\n  },\r\n\r\n  getUnseenChannelsCount: async (): Promise<number> => {\r\n    try {\r\n      let latestSeenMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestSeenMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (!get().channels) {\r\n        await get().fetchChannels();\r\n      }\r\n\r\n      const channels = get().channels;\r\n      if (!channels) return 0;\r\n\r\n      let unseenMessages = {} as Record<string, number>;\r\n      let unseenChannelsCount = 0;\r\n\r\n      const promises = channels.map(async (channel) => {\r\n        const response = await apiClient.get(\r\n          `/messages/channel/${channel.channel_id}?skip=0&limit=1`\r\n        );\r\n\r\n        if (response.data.messages && response.data.messages.length > 0) {\r\n          const latestMessage = response.data.messages[0];\r\n          const latestSeenTimestamp = latestSeenMessages[channel.channel_id];\r\n\r\n          if (\r\n            !latestSeenTimestamp ||\r\n            new Date(latestMessage.created_at) > new Date(latestSeenTimestamp)\r\n          ) {\r\n            unseenMessages[channel.channel_id] = 1;\r\n            unseenChannelsCount++;\r\n          }\r\n        }\r\n      });\r\n\r\n      await Promise.all(promises);\r\n      set({\r\n        unseenMessages,\r\n        unseenChannelsCount,\r\n      });\r\n      return unseenChannelsCount;\r\n    } catch (error) {\r\n      console.error(\"Failed to get unseen channels count:\", error);\r\n      return 0;\r\n    }\r\n  },\r\n\r\n  reconnectWebSocket: () => {\r\n    const currentChannel = get().currentChannel;\r\n    if (currentChannel) {\r\n      const token = localStorage.getItem(\"access_token\");\r\n      if (token) {\r\n        wsManager.disconnect();\r\n        wsManager.connect(currentChannel.channel_id, token);\r\n      }\r\n    } else {\r\n      wsManager.disconnect();\r\n    }\r\n  },\r\n\r\n  isWebSocketConnected: () => {\r\n    return wsManager.isConnected();\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAVA;AAFA;;;;AAcA,MAAM,mBAAmB;AAMzB,MAAM;IACI,SAA2B,KAAK;IAChC,YAA2B,KAAK;IAChC,QAAuB,KAAK;IAC5B,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,oBAAoB,KAAK;IACzB,qBAA4C,KAAK;IACjD,iBAA+C,KAAK;IACpD,sBAA4E,KAAK;IACjF,eAA8C,KAAK;IAE3D,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IAC3C;IAEO,QAAQ,SAAiB,EAAE,KAAa,EAAQ;QACrD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;QACnB;QAEA,IAAI;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS,EAAE,UAAU,OAAO,EAAE,OAAO;YACjF,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU;YAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;YACpC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YAEtC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;QACF;IACF;IAEO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,aAAa,IAAI,CAAC,kBAAkB;YACpC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEO,YAAY,OAAyB,EAAW;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YAC7D,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;YACA,OAAO;QACT;IACF;IAEO,UAAU,OAA4B,EAAQ;QACnD,IAAI,CAAC,cAAc,GAAG;IACxB;IAEO,eAAe,OAAoD,EAAQ;QAChF,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEO,QAAQ,OAA6B,EAAQ;QAClD,IAAI,CAAC,YAAY,GAAG;IACtB;IAEO,cAAuB;QAC5B,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI;IAC1E;IAEQ,WAAW,MAAa,EAAQ;QACtC,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEQ,cAAc,KAAmB,EAAQ;QAC/C,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;YAElC,MAAM,MAAM,IAAI;YAChB,KAAK,WAAW,GAAG,IAAI,WAAW;YAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,IAAI,CAAC,eAAe,GAAG,EAAE;YAC3B;YAEA,MAAM,cAAc,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK;YAE/G,+DAA+D;YAC/D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC5C,+EAA+E;gBAC/E,IAAI,EAAE,WAAW,KAAK,aAAa;oBACjC,MAAM,WAAW,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;oBAC/C,MAAM,cAAc,IAAI,OAAO;oBAC/B,OAAO,AAAC,cAAc,WAAY,MAAM,YAAY;gBACtD;gBACA,OAAO;YACT;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB;gBACA,YAAY,IAAI,WAAW;YAC7B;YAEA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrD;YAEA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEQ,kBAAoE,EAAE,CAAC;IAEvE,YAAY,KAAiB,EAAQ;QAE3C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;YACnD,IAAI,CAAC,SAAS;QAChB;IACF;IAEQ,YAAY,KAAY,EAAQ;QACtC,QAAQ,KAAK,CAAC,oBAAoB;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEQ,YAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACvD;QACF;QAEA,IAAI,CAAC,iBAAiB;QAEtB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,kBAAkB,GAAG,WAAW;YACnC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK;YACzC;QACF,GAAG,IAAI,CAAC,iBAAiB;IAC3B;IAEQ,sBAA8B;QACpC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ,KAAK;QAC9C,MAAM,WAAW,WAAW,SAAS;QAErC,IAAI,aAAa,6DAAwC;QAEzD,aAAa,WAAW,OAAO,CAAC,gBAAgB;QAEhD,OAAO,GAAG,SAAS,EAAE,EAAE,YAAY;IACrC;AACF;AAEA,MAAM,YAAY,IAAI;AAEtB,IAAI,kBAAuD;AAEpD,MAAM,2BAA2B,CAAC;IACvC,kBAAkB;AACpB;AAEO,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC/D,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,YAAY,IAAI;QAChB,iBAAiB,IAAI;QACrB,gBAAgB,CAAC;QACjB,qBAAqB;QACrB,SAAS;QACT,OAAO;QACP,kBAAkB;QAElB,eAAe;YACb,UAAU,SAAS,CAAC,CAAC;gBACnB,MAAM,UAAU;gBAChB,MAAM,QAAQ;gBAGd,IAAI,iBAAiB,WAAW,QAAQ,SAAS,KAAK,gBAAgB,OAAO,EAAE;oBAC7E;gBACF;gBAEA,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF;gBACF;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD;gBACF;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACvC,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,aAAa;oBACf;gBACF;gBAEA,MAAM,UAAU,CAAC;gBAEjB,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,uBAAuB,CAC3B,MAAM,cAAc,CAAC,UAAU,EAC/B,QAAQ,UAAU;gBAEtB;YACF;YAEA,UAAU,cAAc,CAAC,CAAC;gBACxB,IAAI;oBAAE,kBAAkB;gBAAO;YACjC;YAEA,UAAU,OAAO,CAAC,CAAC;gBACjB,IAAI;oBAAE,OAAO;gBAA6B;YAC5C;QACF;QAEA,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAAE,SAAS;gBAAM;YACzD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,eAAe,OAAO,WAAmB,OAAO,CAAC,EAAE,QAAQ,EAAE;YAC3D,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,UAAU,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO;gBAE9D,MAAM,cAAc,SAAS,IAAI,CAAC,QAAQ;gBAE1C,MAAM,aAAa,IAAI,IAAI,MAAM,UAAU;gBAC3C,YAAY,OAAO,CAAC,CAAC,MAAiB,WAAW,GAAG,CAAC,IAAI,EAAE;gBAE3D,IAAI,CAAC,QAAU,CAAC;wBACd,UACE,SAAS,IACL,cACA;+BACM,MAAM,QAAQ,IAAI,EAAE;+BACrB,YAAY,MAAM,CACnB,CAAC,MACC,CAAC,MAAM,QAAQ,EAAE,KACf,CAAC,cAAyB,YAAY,EAAE,KAAK,IAAI,EAAE;yBAG1D;wBACP;wBACA,SAAS;oBACX,CAAC;gBAED,IAAI,UAAU,KAAK,YAAY,MAAM,GAAG,GAAG;oBACzC,MAAM,uBAAuB,CAAC,WAAW,WAAW,CAAC,EAAE,CAAC,UAAU;gBACpE;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,aAAa,OAAO;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,CAAC,gBAAgB;YAErB,MAAM,QAAQ;YACd,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,IAAI,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;YAE9G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;gBACvC,IAAI,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,OAAO;gBAE9C,IAAI,EAAE,OAAO,KAAK,QAAQ,OAAO,EAAE,OAAO;gBAE1C,MAAM,UAAU,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBAC9C,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,OAAO,AAAC,MAAM,UAAW,MAAM,YAAY;YAC7C;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,4BAA4B;YAC5B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAC1C,MAAM,oBAA6B;gBACjC,IAAI;gBACJ,YAAY,eAAe,UAAU;gBACrC,SAAS,QAAQ,OAAO;gBACxB,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,YAAY,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD;YAChC;YAEA,IAAI;gBACF,MAAM,UAAU,CAAC;gBACjB,IAAI,CAAC,QAAU,CAAC;wBACd,iBAAiB,IAAI,IAAI;+BAAI,MAAM,eAAe;4BAAE;yBAAO;oBAC7D,CAAC;gBAED,IAAI,gBAAgC;gBAEpC,IAAI,UAAU,WAAW,IAAI;oBAC3B,MAAM,YAA8B;wBAClC,MAAM;wBACN,SAAS,QAAQ,OAAO;wBACxB,cAAc,QAAQ,YAAY;wBAClC,YAAY,kBAAkB,UAAU;oBAC1C;oBAEA,MAAM,mBAAmB,UAAU,WAAW,CAAC;oBAE/C,IAAI,kBAAkB;wBAGpB,IAAI,CAAC,QAAU,CAAC;gCACd,iBAAiB,IAAI,IAAI;uCAAI,MAAM,eAAe;iCAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;4BAC5E,CAAC;wBAED;oBACF;gBACF;gBAEA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBACjD,cAAc,QAAQ,YAAY;oBAClC,SAAS,QAAQ,OAAO;oBACxB,YAAY,kBAAkB,UAAU;gBAC1C;gBAEA,gBAAgB,SAAS,IAAI;gBAE7B,IAAI,eAAe;oBACjB,MAAM,uBAAuB,CAC3B,eAAe,UAAU,EACzB,cAAc,UAAU;gBAE5B;gBAEA,IAAI,CAAC;oBACH,MAAM,sBAAsB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;oBAEpG,MAAM,2BAA2B,gBAC/B,GAAG,cAAc,SAAS,CAAC,CAAC,EAAE,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG;oBAEvG,MAAM,uBAAuB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA;wBACjE,IAAI,IAAI,EAAE,KAAK,QAAQ,OAAO,OAAO,8BAA8B;wBACnE,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;wBAC3F,OAAO,mBAAmB;oBAC5B;oBAEA,IAAI,uBAAuB,sBAAsB;wBAC/C,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,IAAI,CAAC,eAAe;wBAClB,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,OAAO;wBACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MACpC,IAAI,EAAE,KAAK,SAAS,gBAAgB;wBAEtC,YAAY,IAAI,IACd;+BAAK,MAAM,UAAU,IAAI,EAAE;yBAAE,CAC1B,MAAM,CAAC,CAAC,KAAO,OAAO,QACtB,MAAM,CAAC,cAAc,EAAE;wBAE5B,iBAAiB,IAAI,IACnB;+BAAI,MAAM,eAAe;yBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAErD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM,aAAa,CAAC;gBACpB,MAAM;YACR;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBAChC,GAAG,OAAO;oBACV,YAAY,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QAEA,mBAAmB,CAAC;YAClB,MAAM,mBAAmB,MAAM,cAAc,EAAE;YAE/C,IAAI;gBACF,UAAU;gBACV,YAAY,IAAI;YAClB;YAEA,IAAI,kBAAkB;gBACpB,UAAU,UAAU;YACtB;YAEA,IAAI;gBAAE,gBAAgB;YAAQ;YAE9B,IAAI,SAAS;gBAEX,MAAM,QAAQ,aAAa,OAAO,CAAC;gBAEnC,IAAI,OAAO;oBACT,MAAM,aAAa;oBAEnB,UAAU,OAAO,CAAC,QAAQ,UAAU,EAAE;oBAEtC,MAAM,aAAa,CAAC,QAAQ,UAAU;gBACxC,OAAO;oBACL,IAAI;wBAAE,OAAO;oBAAiC;gBAChD;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,6BAA6B,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACtD,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,4BAA4B;oBAC9B,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,OAAO;oBACL,UAAU;2BAAK,MAAM,QAAQ,IAAI,EAAE;wBAAG;qBAAQ;oBAC9C,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI,MAAM,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,MAAM,UAAU;gBACvF;YACF;QACF;QAEA,eAAe,CAAC;YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;oBAC5D,YAAY,IAAI,IACd;2BAAI,MAAM,UAAU;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAE9C,iBAAiB,IAAI,IACnB;2BAAI,MAAM,eAAe;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;gBAErD,CAAC;QACH;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,yBAAyB,CAAC,WAAmB;YAC3C,IAAI;gBACF,IAAI,iBAAwC,CAAC;gBAC7C,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,iBAAiB,KAAK,KAAK,CAAC;gBAC9B;gBAEA,IAAI,cAAc,CAAC,UAAU,KAAK,WAAW;gBAE7C,cAAc,CAAC,UAAU,GAAG;gBAE5B,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAEtD,IAAI,CAAC,QAAU,CAAC;wBACd,gBAAgB;4BACd,GAAG,MAAM,cAAc;4BACvB,CAAC,UAAU,EAAE;wBACf;wBACA,qBAAqB,KAAK,GAAG,CAAC,GAAG,MAAM,mBAAmB,GAAG;oBAC/D,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;QAEA,wBAAwB;YACtB,IAAI;gBACF,IAAI,qBAA4C,CAAC;gBACjD,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,qBAAqB,KAAK,KAAK,CAAC;gBAClC;gBAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;oBACnB,MAAM,MAAM,aAAa;gBAC3B;gBAEA,MAAM,WAAW,MAAM,QAAQ;gBAC/B,IAAI,CAAC,UAAU,OAAO;gBAEtB,IAAI,iBAAiB,CAAC;gBACtB,IAAI,sBAAsB;gBAE1B,MAAM,WAAW,SAAS,GAAG,CAAC,OAAO;oBACnC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,QAAQ,UAAU,CAAC,eAAe,CAAC;oBAG1D,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;wBAC/D,MAAM,gBAAgB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;wBAC/C,MAAM,sBAAsB,kBAAkB,CAAC,QAAQ,UAAU,CAAC;wBAElE,IACE,CAAC,uBACD,IAAI,KAAK,cAAc,UAAU,IAAI,IAAI,KAAK,sBAC9C;4BACA,cAAc,CAAC,QAAQ,UAAU,CAAC,GAAG;4BACrC;wBACF;oBACF;gBACF;gBAEA,MAAM,QAAQ,GAAG,CAAC;gBAClB,IAAI;oBACF;oBACA;gBACF;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,oBAAoB;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,gBAAgB;gBAClB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,UAAU,UAAU;oBACpB,UAAU,OAAO,CAAC,eAAe,UAAU,EAAE;gBAC/C;YACF,OAAO;gBACL,UAAU,UAAU;YACtB;QACF;QAEA,sBAAsB;YACpB,OAAO,UAAU,WAAW;QAC9B;IACF,CAAC"}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 609, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  Home,\r\n  Calendar,\r\n  User,\r\n  Package,\r\n  CreditCard,\r\n  BookOpen,\r\n  MessageSquare,\r\n  Menu,\r\n  Link as LinkIcon,\r\n  <PERSON><PERSON>,\r\n} from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useWebSocketChat } from \"@/app/hooks/useWebSocketChat\";\r\n\r\ninterface NavItem {\r\n  label: string;\r\n  href: string;\r\n  icon: React.ReactNode;\r\n}\r\n\r\nconst navItems: NavItem[] = [\r\n  {\r\n    label: \"Overview\",\r\n    href: \"/student/dashboard\",\r\n    icon: <Home className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Sessions\",\r\n    href: \"/student/dashboard/sessions\",\r\n    icon: <Calendar className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"AI Counsellor\",\r\n    href: \"/student/dashboard/ai-counsellor\",\r\n    icon: <Bot className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Profile\",\r\n    href: \"/student/dashboard/profile\",\r\n    icon: <User className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Packages\",\r\n    href: \"/student/dashboard/packages\",\r\n    icon: <Package className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Payments\",\r\n    href: \"/student/dashboard/payments\",\r\n    icon: <CreditCard className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Resources\",\r\n    href: \"/student/dashboard/resources\",\r\n    icon: <BookOpen className=\"w-6 h-6\" />,\r\n  },\r\n  {\r\n    label: \"Messages\",\r\n    href: \"/student/dashboard/messages\",\r\n    icon: <MessageSquare className=\"w-6 h-6\" />,\r\n  },\r\n];\r\n\r\ninterface SidebarProps {\r\n  isOpen: boolean;\r\n  onMenuClick: () => void;\r\n}\r\n\r\nconst Sidebar = ({ isOpen, onMenuClick }: SidebarProps) => {\r\n  const pathname = usePathname();\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const { getUnseenChannelsCount, unseenChannelsCount } = useWebSocketChat();\r\n\r\n  useEffect(() => {\r\n    const fetchUnseenCount = async () => {\r\n      await getUnseenChannelsCount();\r\n    };\r\n\r\n    fetchUnseenCount();\r\n  }, []);\r\n\r\n  return (\r\n    <aside\r\n      className={`\r\n        fixed top-0 left-0 h-full bg-white\r\n        transition-all duration-300 ease-in-out\r\n        ${isOpen ? \"w-64 translate-x-0\" : \"w-20 -translate-x-full\"}\r\n        md:translate-x-0\r\n        md:w-20 xl:w-64\r\n        ${isHovered ? \"md:w-64\" : \"\"}\r\n        md:shadow-lg\r\n        shadow-2xl\r\n        z-10\r\n        flex flex-col\r\n\r\n        md:overflow-hidden\r\n        overflow-y-auto\r\n      `}\r\n      onMouseEnter={() => window.innerWidth >= 768 && setIsHovered(true)}\r\n      onMouseLeave={() => window.innerWidth >= 768 && setIsHovered(false)}\r\n    >\r\n      <div className=\"sticky top-0 bg-white z-20 p-4\">\r\n        <div className=\"flex items-center justify-center transition-all duration-300\">\r\n          <Link href=\"/\">\r\n            <Image\r\n              width={100}\r\n              height={100}\r\n              src=\"/icons/logo.png\"\r\n              alt=\"Logo\"\r\n              className=\"transition-all duration-300 w-[3.8rem] h-[3.8rem] md:w-[3.6rem] md:h-[3.6rem] xl:w-[4.8rem] xl:h-[4.8rem] flex-grow-0\"\r\n            />\r\n          </Link>\r\n        </div>\r\n        {isOpen && (\r\n          <button\r\n            className=\"md:hidden absolute right-4 top-1/2 -translate-y-1/2 rounded-lg bg-gray-50 hover:bg-gray-100\"\r\n            onClick={onMenuClick}\r\n          >\r\n            <Menu className=\"w-6 h-6\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex-1 flex flex-col min-h-0\">\r\n        <nav className=\"flex-1 gap-y-4 px-3 flex flex-col\">\r\n          {navItems.map((item) => (\r\n            <Link\r\n              key={item.href}\r\n              href={item.href}\r\n              className={`\r\n                flex items-center p-4 transition-colors rounded-xl\r\n                ${\r\n                  pathname === item.href\r\n                    ? \"bg-gray-900 text-white\"\r\n                    : \"hover:bg-gray-100\"\r\n                }\r\n              `}\r\n              onClick={(e) => {\r\n                onMenuClick();\r\n                if (window.innerWidth >= 768) {\r\n                  setIsHovered(false);\r\n                }\r\n              }}\r\n            >\r\n              <span className=\"flex items-center justify-center\">\r\n                {item.icon}\r\n              </span>\r\n              <span\r\n                className={`\r\n                  mx-4 whitespace-nowrap transition-opacity duration-300\r\n                  ${isOpen ? \"opacity-100\" : \"opacity-0\"}\r\n                  ${isHovered ? \"md:opacity-100\" : \"md:opacity-0\"}\r\n                  xl:opacity-100\r\n                `}\r\n              >\r\n                {item.label}\r\n                {item.label === \"Messages\" && unseenChannelsCount > 0 && (\r\n                  <span className=\"ml-2 px-2 py-1 text-xs bg-green-500 text-white rounded-full\">\r\n                    {unseenChannelsCount}\r\n                  </span>\r\n                )}\r\n              </span>\r\n            </Link>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAaA;AACA;AACA;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;;AA0BA,MAAM,WAAsB;IAC1B;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,mMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IACvB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC3B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;IAC9B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;IACjC;CACD;AAOD,MAAM,UAAU,CAAC,EAAE,MAAM,EAAE,WAAW,EAAgB;IACpD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM;QACR;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC;;;QAGV,EAAE,SAAS,uBAAuB,yBAAyB;;;QAG3D,EAAE,YAAY,YAAY,GAAG;;;;;;;;MAQ/B,CAAC;QACD,cAAc,IAAM,OAAO,UAAU,IAAI,OAAO,aAAa;QAC7D,cAAc,IAAM,OAAO,UAAU,IAAI,OAAO,aAAa;;0BAE7D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,OAAO;gCACP,QAAQ;gCACR,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;oBAIf,wBACC,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC;;gBAEV,EACE,aAAa,KAAK,IAAI,GAClB,2BACA,oBACL;cACH,CAAC;4BACD,SAAS,CAAC;gCACR;gCACA,IAAI,OAAO,UAAU,IAAI,KAAK;oCAC5B,aAAa;gCACf;4BACF;;8CAEA,8OAAC;oCAAK,WAAU;8CACb,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCACC,WAAW,CAAC;;kBAEV,EAAE,SAAS,gBAAgB,YAAY;kBACvC,EAAE,YAAY,mBAAmB,eAAe;;gBAElD,CAAC;;wCAEA,KAAK,KAAK;wCACV,KAAK,KAAK,KAAK,cAAc,sBAAsB,mBAClD,8OAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;;2BA/BF,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAyC5B;uCAEe"}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,6KAAsB,IAAI;AAE/C,MAAM,sBAAsB,6KAAsB,OAAO;AAEzD,MAAM,oBAAoB,6KAAsB,KAAK;AAErD,MAAM,qBAAqB,6KAAsB,MAAM;AAEvD,MAAM,kBAAkB,6KAAsB,GAAG;AAEjD,MAAM,yBAAyB,6KAAsB,UAAU;AAE/D,MAAM,uCAAyB,sMAAM,UAAU,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,sMAAM,UAAU,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,6KAAsB,MAAM;kBAC3B,cAAA,8OAAC,6KAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,6KAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,sMAAM,UAAU,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6KAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,sMAAM,UAAU,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,6KAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,6KAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,sMAAM,UAAU,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,6KAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/image.ts"], "sourcesContent": ["const backgroundColors: Record<string, string> = {\r\n  A: \"#fef3c7\",\r\n  B: \"#fde68a\",\r\n  C: \"#fcd34d\",\r\n  D: \"#fbbf24\",\r\n  E: \"#f9a8d4\",\r\n  F: \"#f472b6\",\r\n  G: \"#ec4899\",\r\n  H: \"#e879f9\",\r\n  I: \"#d8b4fe\",\r\n  J: \"#c4b5fd\",\r\n  K: \"#a78bfa\",\r\n  L: \"#818cf8\",\r\n  M: \"#60a5fa\",\r\n  N: \"#38bdf8\",\r\n  O: \"#22d3ee\",\r\n  P: \"#2dd4bf\",\r\n  Q: \"#34d399\",\r\n  R: \"#4ade80\",\r\n  S: \"#a3e635\",\r\n  T: \"#facc15\",\r\n  U: \"#f97316\",\r\n  V: \"#fb923c\",\r\n  W: \"#f87171\",\r\n  X: \"#ef4444\",\r\n  Y: \"#dc2626\",\r\n  Z: \"#b91c1c\",\r\n};\r\n\r\nexport function generatePlaceholder(\r\n  firstName: string | null | undefined,\r\n  lastName: string | null | undefined\r\n): string {\r\n  if (\r\n    !firstName ||\r\n    typeof firstName !== \"string\" ||\r\n    !firstName.trim() ||\r\n    !lastName\r\n  ) {\r\n    return \"/images/person.png\";\r\n  }\r\n\r\n  const firstLetter = firstName.trim()[0].toUpperCase();\r\n  const secondLetter =\r\n    lastName && typeof lastName === \"string\" && lastName.trim()\r\n      ? lastName.trim()[0].toUpperCase()\r\n      : \"\";\r\n  const initials = firstLetter + secondLetter;\r\n\r\n  const bgColor = backgroundColors[firstLetter] || \"#adb5bd\"; // Default grey shade\r\n  \r\n  return `https://placehold.co/100x100/${bgColor.slice(\r\n    1\r\n  )}/000000?text=${initials}`;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAA2C;IAC/C,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAEO,SAAS,oBACd,SAAoC,EACpC,QAAmC;IAEnC,IACE,CAAC,aACD,OAAO,cAAc,YACrB,CAAC,UAAU,IAAI,MACf,CAAC,UACD;QACA,OAAO;IACT;IAEA,MAAM,cAAc,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW;IACnD,MAAM,eACJ,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,KACrD,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,WAAW,KAC9B;IACN,MAAM,WAAW,cAAc;IAE/B,MAAM,UAAU,gBAAgB,CAAC,YAAY,IAAI,WAAW,qBAAqB;IAEjF,OAAO,CAAC,6BAA6B,EAAE,QAAQ,KAAK,CAClD,GACA,aAAa,EAAE,UAAU;AAC7B"}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/user-dropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LogOut, ChevronDown } from \"lucide-react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuItem,\r\n} from \"@/app/components/ui/dropdown-menu\";\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { UserInfo } from \"@/app/types/student/profile\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface UserDropdownProps {\r\n  userInfo: UserInfo | null;\r\n  onLogoutClick: () => void;\r\n}\r\n\r\nexport const UserDropdown = ({\r\n  userInfo,\r\n  onLogoutClick,\r\n}: UserDropdownProps) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  const handleLogoutClick = (e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    onLogoutClick();\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center space-x-2\">\r\n      <div className=\"flex items-center gap-4\">\r\n        <Image\r\n          className=\"rounded-full object-cover w-[3rem] h-[3rem]\"\r\n          src={\r\n            userInfo?.profile_picture_url ||\r\n            generatePlaceholder(userInfo?.firstName, userInfo?.lastName)\r\n          }\r\n          alt={`${userInfo?.firstName}`}\r\n          width={100}\r\n          height={100}\r\n        />\r\n        <div className=\"sm:flex flex-col hidden\">\r\n          <span className=\"font-medium text-gray-900\">\r\n            {userInfo?.firstName} {userInfo?.lastName}\r\n          </span>\r\n          <span className=\"text-sm text-gray-500\">{userInfo?.email}</span>\r\n        </div>\r\n        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n          <DropdownMenuTrigger asChild>\r\n            <button className=\"outline-none hover:opacity-80\">\r\n              <ChevronDown\r\n                className={`w-6 h-6 transform transition-transform duration-200 ${\r\n                  isOpen ? \"rotate-180\" : \"rotate-0\"\r\n                }`}\r\n              />\r\n            </button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\" className=\"p-2\">\r\n            <DropdownMenuItem\r\n              onClick={handleLogoutClick}\r\n              className=\"flex items-center gap-3 px-4 py-3 text-white bg-red-500 hover:bg-red-600/90 rounded-lg cursor-pointer\"\r\n            >\r\n              <span className=\"font-semibold text-base\">Logout</span>\r\n              <LogOut className=\"w-6 h-6\" />\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAMA;AACA;AAEA;AAVA;AAAA;AAFA;;;;;;;AAmBO,MAAM,eAAe,CAAC,EAC3B,QAAQ,EACR,aAAa,EACK;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACJ,WAAU;oBACV,KACE,UAAU,uBACV,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,WAAW,UAAU;oBAErD,KAAK,GAAG,UAAU,WAAW;oBAC7B,OAAO;oBACP,QAAQ;;;;;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCACb,UAAU;gCAAU;gCAAE,UAAU;;;;;;;sCAEnC,8OAAC;4BAAK,WAAU;sCAAyB,UAAU;;;;;;;;;;;;8BAErD,8OAAC,4IAAA,CAAA,eAAY;oBAAC,MAAM;oBAAQ,cAAc;;sCACxC,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCACV,WAAW,CAAC,oDAAoD,EAC9D,SAAS,eAAe,YACxB;;;;;;;;;;;;;;;;sCAIR,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAM,WAAU;sCACzC,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAC1C,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC"}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@components/lib/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/navbar-skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@/app/components/ui/skeleton\";\r\nimport { Bell, ChevronDown } from \"lucide-react\";\r\n\r\nexport default function NavbarSkeleton() {\r\n    return (\r\n        <nav className=\"flex justify-between items-center p-4\">\r\n            <div className=\"flex-1\" /> {/* Empty space on the left */}\r\n            <div className=\"flex items-center gap-4\">\r\n                <button className=\"p-2 text-gray-900 focus:outline-none rounded-lg bg-white\">\r\n                    <Bell className=\"w-6 h-6\" />\r\n                </button>\r\n                <div className=\"flex items-center space-x-2\">\r\n                    <Skeleton className=\"h-12 w-12 rounded-full\" />\r\n                    <div className=\"flex flex-col gap-1\">\r\n                        <Skeleton className=\"h-4 w-24\" />\r\n                        <Skeleton className=\"h-3 w-32\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <button className=\"outline-none hover:opacity-80\">\r\n          <ChevronDown\r\n            className=\"w-6 h-6 transform transition-transform duration-200 rotate-0\"\r\n          />\r\n        </button>\r\n        </nav>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEe,SAAS;IACpB,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;;;;;YAAW;0BAC1B,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAO,WAAU;kCACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAIhC,8OAAC;gBAAO,WAAU;0BACpB,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBACV,WAAU;;;;;;;;;;;;;;;;;AAKtB"}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM;IACN,MAAM;IACN,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1841, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Bell, <PERSON>u } from \"lucide-react\";\r\nimport { UserDropdown } from \"./user-dropdown\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport NavbarSkeleton from \"./navbar-skeleton\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface NavbarProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nconst Navbar = ({ onMenuClick }: NavbarProps) => {\r\n  const { userInfo } = useProfile();\r\n\r\n  const { logout } = useAuth();\r\n  const router = useRouter();\r\n  const handleLogout = async () => {\r\n    try {\r\n      logout();\r\n      router.push(\"/auth/login\");\r\n    } catch (error) {\r\n      console.error(\"Logout failed:\", error);\r\n    }\r\n  };\r\n\r\n  return !userInfo ? (\r\n    <NavbarSkeleton />\r\n  ) : (\r\n    <nav className=\"flex justify-between items-center p-4\">\r\n      <button\r\n        className=\"md:hidden p-2 text-gray-900 focus:outline-none rounded-lg bg-white\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <Menu className=\"w-6 h-6\" />\r\n      </button>\r\n      <div className=\"flex items-center space-x-4 ml-auto\">\r\n        <button className=\"p-3 text-gray-900 focus:outline-none rounded-xl bg-white hover:bg-gray-100 transition-all\">\r\n          <Bell className=\"w-6 h-6\" />\r\n        </button>\r\n\r\n        <UserDropdown\r\n          userInfo={userInfo}\r\n          onLogoutClick={() => handleLogout()}\r\n        />\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AALA;AAAA;AAFA;;;;;;;;AAaA,MAAM,SAAS,CAAC,EAAE,WAAW,EAAe;IAC1C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAE9B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe;QACnB,IAAI;YACF;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,OAAO,CAAC,yBACN,8OAAC,mJAAA,CAAA,UAAc;;;;6BAEf,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC,iJAAA,CAAA,eAAY;wBACX,UAAU;wBACV,eAAe,IAAM;;;;;;;;;;;;;;;;;;AAK/B;uCAEe"}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1939, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport Sidebar from \"@components/student/sidebar\";\r\nimport Navbar from \"@components/student/navbar\";\r\nimport { AppToastContainer } from \"@/app/components/common/toastContainer\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\nexport default function Layout({ children }: LayoutProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { userInfo, fetchUserInfo } = useProfile();\r\n  const [intialFetchDone, setIntialFetchDone] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!localStorage.getItem(\"access_token\")) {\r\n      router.replace(\"/auth/login\");\r\n      return;\r\n    }\r\n    fetchUserInfo();\r\n    setIntialFetchDone(true);\r\n  }, [fetchUserInfo]);\r\n\r\n  useEffect(() => {\r\n    if (intialFetchDone && userInfo) {\r\n      // If not a student, redirect to counselor dashboard\r\n      if (userInfo.userType !== \"student\") {\r\n        router.replace(\"/counselor/dashboard\");\r\n        return;\r\n      }\r\n\r\n      // Allowing dashboard access regardless of profile completion status\r\n    }\r\n  }, [userInfo, router, pathname]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-100\">\r\n      <AppToastContainer />\r\n      <Sidebar\r\n        isOpen={isSidebarOpen}\r\n        onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n      />\r\n      <div className=\"flex flex-col min-h-screen md:ml-20 xl:ml-64\">\r\n        <Navbar onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)} />\r\n        <main className=\"sm:p-6 p-2\">{children}</main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAYe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,OAAO,CAAC,iBAAiB;YACzC,OAAO,OAAO,CAAC;YACf;QACF;QACA;QACA,mBAAmB;IACrB,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,UAAU;YAC/B,oDAAoD;YACpD,IAAI,SAAS,QAAQ,KAAK,WAAW;gBACnC,OAAO,OAAO,CAAC;gBACf;YACF;QAEA,oEAAoE;QACtE;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAA,CAAA,oBAAiB;;;;;0BAClB,8OAAC,wIAAA,CAAA,UAAO;gBACN,QAAQ;gBACR,aAAa,IAAM,iBAAiB,CAAC;;;;;;0BAEvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uIAAA,CAAA,UAAM;wBAAC,aAAa,IAAM,iBAAiB,CAAC;;;;;;kCAC7C,8OAAC;wBAAK,WAAU;kCAAc;;;;;;;;;;;;;;;;;;AAItC"}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}