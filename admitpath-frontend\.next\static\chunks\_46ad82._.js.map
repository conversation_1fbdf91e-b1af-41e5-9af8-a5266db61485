{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAGW;AALX;;AAIA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/useProfile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { ProfileState } from \"@/app/types/counselor/profile\";\r\n\r\nexport const useProfile = create<ProfileState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  userInfo: null,\r\n  personalInfo: null,\r\n  educationInfo: null,\r\n  experienceInfo: null,\r\n  documentInfo: null,\r\n  profilePicture: null,\r\n\r\n  getUserInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`\r\n      );\r\n      set({ userInfo: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getPersonalInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/counselor/profile/personal-info\");\r\n      set({ personalInfo: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getEducationInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/counselor/profile/education\");\r\n      set({ educationInfo: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getExperienceInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        \"/counselor/profile/professional-experience\"\r\n      );\r\n      set({ experienceInfo: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getDocumentInfo: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/counselor/profile/documents\");\r\n      set({ documentInfo: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  // Upload Profile Picture\r\n  uploadProfilePicture: async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files ? e.target.files[0] : null;\r\n    const validFiles = [\"image/png\", \"image/jpeg\", \"image/jpg\", \"image/webp\"];\r\n\r\n    if (!file) {\r\n      alert(\"No image selected.\");\r\n      return;\r\n    }\r\n\r\n    if (!validFiles.includes(file.type)) {\r\n      alert(\r\n        \"Invalid file type. Please upload an image in png, jpeg, jpg, or webp format.\"\r\n      );\r\n      return;\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append(\"file\", file);\r\n\r\n    try {\r\n      const response = await apiClient.post(\"/user/profile-picture\", formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n\r\n      set({ profilePicture: response.data.profile_picture_url });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  deleteProfilePicture: async () => {\r\n    try {\r\n      // The correct endpoint should be the same as the backend's configured route\r\n      await apiClient.delete(\"/user/profile-picture\");\r\n      // After deletion, refetch user info to update the UI\r\n      const { getUserInfo } = get();\r\n      await getUserInfo();\r\n      set({ profilePicture: null });\r\n    } catch (error: any) {\r\n      console.error(\"Error deleting profile picture:\", error);\r\n      set({ error: error.response?.data?.detail || \"Something went wrong\" });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAMO,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,UAAU;QACV,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAEhB,aAAa;YACX,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAAE;gBAEzE,IAAI;oBAAE,UAAU,SAAS,IAAI;gBAAC;YAChC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;gBAAC;YACpC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB;YAChB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,eAAe,SAAS,IAAI;gBAAC;YACrC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,mBAAmB;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC;gBAEF,IAAI;oBAAE,gBAAgB,SAAS,IAAI;gBAAC;YACtC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,cAAc,SAAS,IAAI;gBAAC;YACpC,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,yBAAyB;QACzB,sBAAsB,OAAO;YAC3B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;YAClD,MAAM,aAAa;gBAAC;gBAAa;gBAAc;gBAAa;aAAa;YAEzE,IAAI,CAAC,MAAM;gBACT,MAAM;gBACN;YACF;YAEA,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACnC,MACE;gBAEF;YACF;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB,UAAU;oBACvE,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI;oBAAE,gBAAgB,SAAS,IAAI,CAAC,mBAAmB;gBAAC;YAC1D,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR;QACF;QAEA,sBAAsB;YACpB,IAAI;gBACF,4EAA4E;gBAC5E,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;gBACvB,qDAAqD;gBACrD,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,MAAM;gBACN,IAAI;oBAAE,gBAAgB;gBAAK;YAC7B,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAuB;gBACpE,MAAM;YACR;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/counselor/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\n\r\nexport default function CounselorPage({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { userInfo, getUserInfo } = useProfile();\r\n\r\n  useEffect(() => {\r\n    if (!localStorage.getItem(\"access_token\")) {\r\n      router.replace(\"/auth/login\");\r\n      return;\r\n    }\r\n    getUserInfo();\r\n  }, [getUserInfo]);\r\n\r\n  useEffect(() => {\r\n    if (userInfo) {\r\n      const { isProfileComplete, is_verified } = userInfo;\r\n\r\n      // If not a counselor, redirect to student dashboard\r\n      if (userInfo.userType !== \"counselor\") {\r\n        router.replace(\"/student/dashboard\");\r\n        return;\r\n      }\r\n\r\n      const isOnboardingPath = pathname?.startsWith(\"/counselor/onboarding\");\r\n      const isDashboardPath = pathname?.startsWith(\"/counselor/dashboard\");\r\n\r\n      // If profile is not complete, only allow access to onboarding\r\n      if (!isProfileComplete && !isOnboardingPath) {\r\n        router.replace(\"/counselor/onboarding\");\r\n        return;\r\n      }\r\n\r\n      // If profile is complete but not verified, show completion page\r\n      if (\r\n        isProfileComplete &&\r\n        !is_verified &&\r\n        (isDashboardPath || isOnboardingPath)\r\n      ) {\r\n        router.replace(\"/counselor/profile-complete\");\r\n        return;\r\n      }\r\n\r\n      // If profile is complete and verified, don't allow access to onboarding\r\n      if (isProfileComplete && is_verified && isOnboardingPath) {\r\n        router.replace(\"/counselor/dashboard\");\r\n        return;\r\n      }\r\n    }\r\n  }, [userInfo, router, pathname]);\r\n\r\n  return children;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAMe,SAAS,cAAc,EACpC,QAAQ,EAGT;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,aAAa,OAAO,CAAC,iBAAiB;gBACzC,OAAO,OAAO,CAAC;gBACf;YACF;YACA;QACF;kCAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,UAAU;gBACZ,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG;gBAE3C,oDAAoD;gBACpD,IAAI,SAAS,QAAQ,KAAK,aAAa;oBACrC,OAAO,OAAO,CAAC;oBACf;gBACF;gBAEA,MAAM,mBAAmB,UAAU,WAAW;gBAC9C,MAAM,kBAAkB,UAAU,WAAW;gBAE7C,8DAA8D;gBAC9D,IAAI,CAAC,qBAAqB,CAAC,kBAAkB;oBAC3C,OAAO,OAAO,CAAC;oBACf;gBACF;gBAEA,gEAAgE;gBAChE,IACE,qBACA,CAAC,eACD,CAAC,mBAAmB,gBAAgB,GACpC;oBACA,OAAO,OAAO,CAAC;oBACf;gBACF;gBAEA,wEAAwE;gBACxE,IAAI,qBAAqB,eAAe,kBAAkB;oBACxD,OAAO,OAAO,CAAC;oBACf;gBACF;YACF;QACF;kCAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,OAAO;AACT;GAvDwB;;QAKP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACM,0IAAA,CAAA,aAAU;;;KAPtB"}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}