{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useOverview.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { OverviewState, ReviewData } from \"@/app/types/student/overview\";\r\n\r\nexport const useOverview = create<OverviewState>((set) => ({\r\n  loading: false,\r\n  error: null,\r\n  stats: null,\r\n  pendingFeedbacks: [],\r\n  totalPendingFeedbacks: 0,\r\n\r\n  fetchStats: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/student/dashboard/stats\");\r\n      set({ stats: response.data });\r\n    } catch (error: any) {\r\n      set({ error: error.response?.data?.detail || \"Failed to fetch stats\" });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchPendingFeedbacks: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/pending-feedback?timezone=${\r\n          Intl.DateTimeFormat().resolvedOptions().timeZone\r\n        }`\r\n      );\r\n      set({\r\n        pendingFeedbacks: response.data.sessions,\r\n        totalPendingFeedbacks: response.data.total,\r\n      });\r\n    } catch (error: any) {\r\n      set({\r\n        error:\r\n          error.response?.data?.detail || \"Failed to fetch pending feedbacks\",\r\n      });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  submitFeedback: async (sessionId: number, data: ReviewData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.post(`/sessions/${sessionId}/reviews`, data);\r\n      // Refresh pending feedbacks after submission\r\n      const response = await apiClient.get(\r\n        `/pending-feedback?timezone=${\r\n          Intl.DateTimeFormat().resolvedOptions().timeZone\r\n        }`\r\n      );\r\n      set({\r\n        pendingFeedbacks: response.data.sessions,\r\n        totalPendingFeedbacks: response.data.total,\r\n      });\r\n    } catch (error: any) {\r\n      set({\r\n        loading: false,\r\n        error: error.response?.data?.detail || \"Failed to submit feedback\",\r\n      });\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAMO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QACzD,SAAS;QACT,OAAO;QACP,OAAO;QACP,kBAAkB,EAAE;QACpB,uBAAuB;QAEvB,YAAY;YACV,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,OAAO,SAAS,IAAI;gBAAC;YAC7B,EAAE,OAAO,OAAY;gBACnB,IAAI;oBAAE,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAAwB;gBACrE,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,uBAAuB;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,2BAA2B,EAC1B,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;gBAEJ,IAAI;oBACF,kBAAkB,SAAS,IAAI,CAAC,QAAQ;oBACxC,uBAAuB,SAAS,IAAI,CAAC,KAAK;gBAC5C;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OACE,MAAM,QAAQ,EAAE,MAAM,UAAU;gBACpC;gBACA,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO,WAAmB;YACxC,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,EAAE;gBACvD,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,2BAA2B,EAC1B,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;gBAEJ,IAAI;oBACF,kBAAkB,SAAS,IAAI,CAAC,QAAQ;oBACxC,uBAAuB,SAAS,IAAI,CAAC,KAAK;gBAC5C;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,SAAS;oBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;gBACzC;gBACA,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@/app/components/ui/skeleton\";\r\n\r\nexport const StatsSkeleton = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n      {[...Array(3)].map((_, i) => (\r\n        <div key={i} className=\"flex items-center gap-4 p-4 border rounded-xl\">\r\n          <Skeleton className=\"h-12 w-12 rounded-xl\" /> {/* Icon */}\r\n          <div className=\"flex flex-row sm:flex-col gap-2\">\r\n            <Skeleton className=\"h-6 w-16\" /> {/* Number */}\r\n            <Skeleton className=\"h-4 w-32\" /> {/* Label */}\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const UpcomingSessionsSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <Skeleton className=\"h-8 w-48\" /> {/* Title */}\r\n      {[...Array(3)].map((_, i) => (\r\n        <div key={i} className=\"flex items-center gap-4 p-4 border rounded-xl\">\r\n          <Skeleton className=\"h-12 w-12 rounded-full\" /> {/* Avatar */}\r\n          <div className=\"space-y-2 flex-1\">\r\n            <Skeleton className=\"h-5 w-3/4\" /> {/* Session name */}\r\n            <Skeleton className=\"h-4 w-1/2\" /> {/* Counselor name */}\r\n            <Skeleton className=\"h-4 w-1/3\" /> {/* Time */}\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const PendingFeedbacksSkeleton = () => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <Skeleton className=\"h-8 w-48\" /> {/* Title */}\r\n      <div className=\"p-4 border rounded-xl space-y-2\">\r\n        <Skeleton className=\"h-5 w-full\" />\r\n        <Skeleton className=\"h-4 w-3/4\" />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAEO,MAAM,gBAAgB;IAC3B,qBACE,8OAAC;QAAI,WAAU;kBACZ;eAAI,MAAM;SAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAyB;kCAC7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAa;0CACjC,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAa;;;;;;;;eAJ3B;;;;;;;;;;AAUlB;AAEO,MAAM,2BAA2B;IACtC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAa;YAChC;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAA2B;sCAC/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAc;8CAClC,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAc;8CAClC,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAc;;;;;;;;mBAL5B;;;;;;;;;;;AAWlB;AAEO,MAAM,2BAA2B;IACtC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAa;0BACjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI5B"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/stats-card.tsx"], "sourcesContent": ["import { Video, Calendar, Users } from \"lucide-react\";\r\nimport { useOverview } from \"@hooks/student/useOverview\";\r\nimport { StatsSkeleton } from \"./skeleton\";\r\n\r\nexport const StatsCards = () => {\r\n  const { stats } = useOverview();\r\n\r\n  const cards = [\r\n    {\r\n      icon: <Video className=\"w-6 h-6 text-purple-600\" />,\r\n      value: stats?.total_sessions || 0,\r\n      label: \"Total Sessions Booked\",\r\n      bgColor: \"bg-purple-100\",\r\n    },\r\n    {\r\n      icon: <Calendar className=\"w-6 h-6 text-orange-600\" />,\r\n      value: stats?.upcoming_sessions || 0,\r\n      label: \"Upcoming Sessions\",\r\n      bgColor: \"bg-orange-100\",\r\n    },\r\n    {\r\n      icon: <Users className=\"w-6 h-6 text-blue-600\" />,\r\n      value: stats?.total_counsellors || 0,\r\n      label: \"Total Counsellors\",\r\n      bgColor: \"bg-blue-100\",\r\n    },\r\n  ];\r\n\r\n  return !stats ? (\r\n    <StatsSkeleton />\r\n  ) : (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n      {cards.map((card, index) => (\r\n        <div\r\n          key={index}\r\n          className=\"flex items-center gap-4 md:p-6 p-4 rounded-2xl border\"\r\n        >\r\n          <div className={`p-3 ${card.bgColor} rounded-xl`}>{card.icon}</div>\r\n          <div className=\"flex flex-row md:flex-col items-center md:items-start gap-2\">\r\n            <h3 className=\"md:text-4xl text-2xl font-medium\">{card.value}</h3>\r\n            <p className=\"md:text-lg text-md text-gray-600\">{card.label}</p>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;AAAA;AAAA;;;;;AAIO,MAAM,aAAa;IACxB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE5B,MAAM,QAAQ;QACZ;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO,OAAO,kBAAkB;YAChC,OAAO;YACP,SAAS;QACX;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO,OAAO,qBAAqB;YACnC,OAAO;YACP,SAAS;QACX;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO,OAAO,qBAAqB;YACnC,OAAO;YACP,SAAS;QACX;KACD;IAED,OAAO,CAAC,sBACN,8OAAC,qJAAA,CAAA,gBAAa;;;;6BAEd,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAU;;kCAEV,8OAAC;wBAAI,WAAW,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC;kCAAG,KAAK,IAAI;;;;;;kCAC5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC,KAAK,KAAK;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAAoC,KAAK,KAAK;;;;;;;;;;;;;eANxD;;;;;;;;;;AAYf"}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useSessions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { NotesPayload, SessionsState } from \"@/app/types/student/sessions\";\r\n\r\nexport const useSessions = create<SessionsState>((set, get) => ({\r\n    loading: false,\r\n    error: null,\r\n    sessions: { items: [], total: null },\r\n    currentNotes: null,\r\n\r\n    fetchSessions: async (timezone, params) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/student/sessions/my-sessions?timezone=${timezone}`,\r\n                {\r\n                    params,\r\n                }\r\n            );\r\n            set({\r\n                sessions: {\r\n                    items: response.data.items || [],\r\n                    total: response.data.total || 0,\r\n                },\r\n                loading: false,\r\n            });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to fetch sessions\",\r\n                loading: false,\r\n                sessions: { items: [], total: 0 },\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    fetchSessionNotes: async (sessionId: number) => {\r\n        if (!sessionId) return null;\r\n\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.get(\r\n                `/sessions/${sessionId}/notes`\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to fetch session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    createSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to create session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    updateSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.put(\r\n                `/sessions/${sessionId}/notes`,\r\n                data\r\n            );\r\n            set({ currentNotes: response.data, loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to update session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    deleteSessionNotes: async (sessionId: number) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            await apiClient.delete(`/sessions/${sessionId}/notes`);\r\n            set({ currentNotes: null, loading: false });\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail ||\r\n                    \"Failed to delete session notes\",\r\n                loading: false,\r\n                currentNotes: null,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n\r\n    clearError: () => {\r\n        set({ error: null });\r\n    },\r\n\r\n    createSession: async (counselorId: number, data) => {\r\n        try {\r\n            set({ loading: true, error: null });\r\n            const response = await apiClient.post(\r\n                `/student/sessions/counselor/${counselorId}/sessions`,\r\n                {\r\n                    session: {\r\n                        service_type: data.service_type,\r\n                        description: data.description,\r\n                        date: data.date,\r\n                        start_time: data.start_time,\r\n                        end_time: data.end_time,\r\n                    },\r\n                    payment: {\r\n                        amount: data.amount,\r\n                        promo_code: data.promo_code,\r\n                    },\r\n                }\r\n            );\r\n            set({ loading: false });\r\n            return response.data;\r\n        } catch (error: any) {\r\n            set({\r\n                error:\r\n                    error.response?.data?.detail || \"Failed to create session\",\r\n                loading: false,\r\n            });\r\n            throw error;\r\n        }\r\n    },\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AADA;AAFA;;;AAMO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QAC5D,SAAS;QACT,OAAO;QACP,UAAU;YAAE,OAAO,EAAE;YAAE,OAAO;QAAK;QACnC,cAAc;QAEd,eAAe,OAAO,UAAU;YAC5B,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,uCAAuC,EAAE,UAAU,EACpD;oBACI;gBACJ;gBAEJ,IAAI;oBACA,UAAU;wBACN,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;wBAChC,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI;oBAClC;oBACA,SAAS;gBACb;gBACA,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;oBACT,UAAU;wBAAE,OAAO,EAAE;wBAAE,OAAO;oBAAE;gBACpC;gBACA,MAAM;YACV;QACJ;QAEA,mBAAmB,OAAO;YACtB,IAAI,CAAC,WAAW,OAAO;YAEvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBAElC,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO,WAAmB;YAC1C,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAChC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC,EAC9B;gBAEJ,IAAI;oBAAE,cAAc,SAAS,IAAI;oBAAE,SAAS;gBAAM;gBAClD,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,oBAAoB,OAAO;YACvB,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBACrD,IAAI;oBAAE,cAAc;oBAAM,SAAS;gBAAM;YAC7C,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UACtB;oBACJ,SAAS;oBACT,cAAc;gBAClB;gBACA,MAAM;YACV;QACJ;QAEA,YAAY;YACR,IAAI;gBAAE,OAAO;YAAK;QACtB;QAEA,eAAe,OAAO,aAAqB;YACvC,IAAI;gBACA,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACjC,CAAC,4BAA4B,EAAE,YAAY,SAAS,CAAC,EACrD;oBACI,SAAS;wBACL,cAAc,KAAK,YAAY;wBAC/B,aAAa,KAAK,WAAW;wBAC7B,MAAM,KAAK,IAAI;wBACf,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;oBAC3B;oBACA,SAAS;wBACL,QAAQ,KAAK,MAAM;wBACnB,YAAY,KAAK,UAAU;oBAC/B;gBACJ;gBAEJ,IAAI;oBAAE,SAAS;gBAAM;gBACrB,OAAO,SAAS,IAAI;YACxB,EAAE,OAAO,OAAY;gBACjB,IAAI;oBACA,OACI,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACpC,SAAS;gBACb;gBACA,MAAM;YACV;QACJ;IACJ,CAAC"}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,mKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,sMAAM,UAAU,CAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,sMAAM,UAAU,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,sMAAM,UAAU,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/send-message-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Textarea } from \"@components/ui/textarea\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { useWebSocketChat } from \"@/app/hooks/useWebSocketChat\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nconst messageSchema = z.object({\r\n  message: z\r\n    .string()\r\n    .min(1, \"Message is required\")\r\n    .max(500, \"Message cannot exceed 500 characters\"),\r\n});\r\n\r\ntype MessageFormData = z.infer<typeof messageSchema>;\r\n\r\ninterface CounselorInfo {\r\n  user_id: number;\r\n  first_name: string;\r\n  last_name: string;\r\n  profile_picture_url: string | null;\r\n}\r\n\r\ninterface SendMessageDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  counselorInfo: CounselorInfo;\r\n}\r\n\r\nexport function SendMessageDialog({\r\n  open,\r\n  onOpenChange,\r\n  counselorInfo,\r\n}: SendMessageDialogProps) {\r\n  const [isSending, setIsSending] = useState(false);\r\n  const router = useRouter();\r\n  const { userInfo } = useProfile();\r\n  const { sendInitialMessage } = useWebSocketChat();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<MessageFormData>({\r\n    resolver: zodResolver(messageSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: MessageFormData) => {\r\n    if (!userInfo) return;\r\n\r\n    setIsSending(true);\r\n    try {\r\n      await sendInitialMessage({\r\n        message: data.message,\r\n        recipient_id: counselorInfo?.user_id,\r\n      });\r\n\r\n      toast.success(\"Message sent successfully!\");\r\n      reset();\r\n      onOpenChange(false);\r\n      router.push(\"/student/dashboard/messages\");\r\n    } catch (error: any) {\r\n      if (\r\n        error?.response?.data?.detail?.includes(\r\n          \"between a counselor and a student\"\r\n        )\r\n      ) {\r\n        toast.error(\"You cannot send any messages to this person.\");\r\n      } else {\r\n        toast.error(\"Failed to send message. Please try again.\");\r\n      }\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold text-center\">\r\n            Send message to:\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex items-center space-x-4 mt-4\">\r\n          <Avatar className=\"w-16 h-16\">\r\n            <AvatarImage\r\n              src={\r\n                counselorInfo?.profile_picture_url ||\r\n                generatePlaceholder(\r\n                  counselorInfo?.first_name,\r\n                  counselorInfo?.last_name\r\n                )\r\n              }\r\n              alt={counselorInfo?.first_name || \"\"}\r\n            />\r\n            <AvatarFallback>{counselorInfo?.first_name || \"\"}</AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex flex-col\">\r\n            <h3 className=\"text-xl font-semibold\">\r\n              {counselorInfo\r\n                ? `${counselorInfo.first_name} ${counselorInfo.last_name}`\r\n                : \"loading...\"}\r\n            </h3>\r\n          </div>\r\n        </div>\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4 mt-4\">\r\n          <div>\r\n            <Textarea\r\n              {...register(\"message\")}\r\n              placeholder=\"Type your message here...\"\r\n              className=\"min-h-[100px]\"\r\n            />\r\n            {errors.message && (\r\n              <p className=\"text-red-500 text-sm mt-1\">\r\n                {errors.message.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-blue-800 hover:bg-blue-800/90\"\r\n            disabled={isSending}\r\n          >\r\n            {isSending ? \"Sending...\" : \"Send Message\"}\r\n          </Button>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAFA;AAHA;;;;;;;;;;;;;;;AAqBA,MAAM,gBAAgB,qIAAE,MAAM,CAAC;IAC7B,SAAS,qIACN,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,KAAK;AACd;AAiBO,SAAS,kBAAkB,EAChC,IAAI,EACJ,YAAY,EACZ,aAAa,EACU;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,IAAI;YACF,MAAM,mBAAmB;gBACvB,SAAS,KAAK,OAAO;gBACrB,cAAc,eAAe;YAC/B;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,aAAa;YACb,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,IACE,OAAO,UAAU,MAAM,QAAQ,SAC7B,sCAEF;gBACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAiC;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KACE,eAAe,uBACf,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,eAAe,YACf,eAAe;oCAGnB,KAAK,eAAe,cAAc;;;;;;8CAEpC,8OAAC,kIAAA,CAAA,iBAAc;8CAAE,eAAe,cAAc;;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,gBACG,GAAG,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,SAAS,EAAE,GACxD;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,8OAAC;;8CACC,8OAAC,oIAAA,CAAA,WAAQ;oCACN,GAAG,SAAS,UAAU;oCACvB,aAAY;oCACZ,WAAU;;;;;;gCAEX,OAAO,OAAO,kBACb,8OAAC;oCAAE,WAAU;8CACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;sCAI7B,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMxC"}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/upcoming-sessions.tsx"], "sourcesContent": ["import { Calendar, Clock, Video } from \"lucide-react\";\r\nimport { useSessions } from \"@hooks/student/useSessions\";\r\nimport { UpcomingSessionsSkeleton } from \"./skeleton\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { SendMessageDialog } from \"@/app/components/student/sessions/send-message-dialog\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { Session } from \"@/app/types/student/sessions\";\r\n\r\n// Helper function to check if a session needs feedback - same as in sessions/index.tsx\r\nconst isPendingFeedback = (session: Session): boolean => {\r\n  if (session.status === \"upcoming\") {\r\n    const endTime = new Date(session.end_time);\r\n    const currentTime = new Date();\r\n    return endTime < currentTime;\r\n  }\r\n  return false;\r\n};\r\n\r\nexport const UpcomingSessions = () => {\r\n  const { sessions } = useSessions();\r\n  const [showSendMessage, setShowSendMessage] = useState(false);\r\n  const [selectedCounselorInfo, setSelectedCounselorInfo] = useState<any>(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (sessions) {\r\n      setLoading(false);\r\n    }\r\n  }, [sessions]);\r\n\r\n  const formatTime = (startTime: string, endTime: string) => {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    return `${start.toLocaleDateString([], {\r\n      month: \"short\",\r\n      day: \"numeric\",\r\n    })} :- ${start.toLocaleTimeString([], {\r\n      hour: \"numeric\",\r\n      minute: \"2-digit\",\r\n    })} - ${end.toLocaleTimeString([], {\r\n      hour: \"numeric\",\r\n      minute: \"2-digit\",\r\n    })}`;\r\n  };\r\n\r\n  return !sessions ? (\r\n    <UpcomingSessionsSkeleton />\r\n  ) : (\r\n    <div className=\"rounded-2xl border flex flex-col gap-6 md:p-6 p-4\">\r\n      <h3 className=\"text-xl font-medium\">Upcoming Sessions</h3>\r\n\r\n      {/* Message dialog */}\r\n      {selectedCounselorInfo && (\r\n        <SendMessageDialog\r\n          open={showSendMessage}\r\n          onOpenChange={setShowSendMessage}\r\n          counselorInfo={selectedCounselorInfo}\r\n        />\r\n      )}\r\n\r\n      {sessions.items.filter(\r\n        (session) =>\r\n          session.status === \"upcoming\" && !isPendingFeedback(session)\r\n      ).length > 0 ? (\r\n        <div className=\"space-y-4\">\r\n          {sessions.items\r\n            .filter(\r\n              (session) =>\r\n                session.status === \"upcoming\" && !isPendingFeedback(session)\r\n            )\r\n            .map((session) => (\r\n              <div\r\n                key={session.id}\r\n                className=\"p-4 bg-gray-50 border rounded-2xl hover:border-gray-300 hover:shadow-sm transition-all duration-200\"\r\n              >\r\n                <div className=\"flex flex-col gap-4\">\r\n                  {/* Top row: Session info and join button */}\r\n                  <div className=\"flex sm:flex-row flex-col sm:items-start items-center justify-between gap-x-4 gap-y-5\">\r\n                    <div className=\"flex items-start gap-3 flex-grow\">\r\n                      <div className=\"p-2 bg-purple-100 rounded-xl flex-shrink-0\">\r\n                        <Video className=\"w-6 h-6 text-purple-600\" />\r\n                      </div>\r\n                      <div>\r\n                        <h3 className=\"text-lg font-medium mb-1\">\r\n                          {session.event_name}\r\n                        </h3>\r\n                        <div className=\"flex items-center gap-1 text-gray-500\">\r\n                          <Clock className=\"w-4 h-4\" />\r\n                          <span>\r\n                            {formatTime(session.start_time, session.end_time)}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex 2xl:flex-row sm:flex-col flex-row gap-2\">\r\n                      {session.meeting_link ? (\r\n                        <Link\r\n                          href={session.meeting_link}\r\n                          target=\"_blank\"\r\n                          rel=\"noopener noreferrer\"\r\n                          className=\"px-4 py-2 bg-navy-700 bg-gray-800 text-white rounded-lg hover:bg-navy-800 transition-colors whitespace-nowrap flex-shrink-0\"\r\n                        >\r\n                          Join Session\r\n                        </Link>\r\n                      ) : (\r\n                        <button\r\n                          disabled\r\n                          className=\"px-4 py-2 bg-gray-400 text-white rounded-lg whitespace-nowrap flex-shrink-0 cursor-not-allowed\"\r\n                        >\r\n                          No Link Yet\r\n                        </button>\r\n                      )}\r\n                      <button\r\n                        onClick={() => {\r\n                          setSelectedCounselorInfo({\r\n                            user_id: session.counselor_user_id,\r\n                            first_name:\r\n                              session.counselor_name?.split(\" \")[0] || \"\",\r\n                            last_name:\r\n                              session.counselor_name?.split(\" \")[1] || \"\",\r\n                            profile_picture_url:\r\n                              session.counselor_profile_picture || null,\r\n                          });\r\n                          setShowSendMessage(true);\r\n                        }}\r\n                        className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors whitespace-nowrap flex-shrink-0\"\r\n                      >\r\n                        Message\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Bottom row: Counselor info */}\r\n                  <div className=\"flex items-center gap-3 border-t pt-3\">\r\n                    <Image\r\n                      src={\r\n                        session.counselor_profile_picture ||\r\n                        generatePlaceholder(\r\n                          session.counselor_name?.split(\" \")[0] || \"\",\r\n                          session.counselor_name?.split(\" \")[1] || \"\"\r\n                        )\r\n                      }\r\n                      alt=\"Counselor\"\r\n                      width={40}\r\n                      height={40}\r\n                      className=\"rounded-full object-cover w-10 h-10 flex-shrink-0\"\r\n                    />\r\n                    <div>\r\n                      <div className=\"font-medium\">\r\n                        {session.counselor_name || \"\"}\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-500\">Counselor</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n        </div>\r\n      ) : loading ? (\r\n        <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n          <Calendar className=\"w-12 h-12 text-gray-400 mb-3\" />\r\n          <p className=\"text-gray-600 font-medium\">Loading...</p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n          <Calendar className=\"w-12 h-12 text-gray-400 mb-3\" />\r\n          <p className=\"text-gray-600 font-medium\">No upcoming sessions</p>\r\n          <p className=\"text-gray-500 text-sm\">\r\n            Book a session with one of our counselors\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AAAA;AAAA;;;;;;;;;;AAUA,uFAAuF;AACvF,MAAM,oBAAoB,CAAC;IACzB,IAAI,QAAQ,MAAM,KAAK,YAAY;QACjC,MAAM,UAAU,IAAI,KAAK,QAAQ,QAAQ;QACzC,MAAM,cAAc,IAAI;QACxB,OAAO,UAAU;IACnB;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB;IAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC,WAAmB;QACrC,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI,KAAK;QACrB,OAAO,GAAG,MAAM,kBAAkB,CAAC,EAAE,EAAE;YACrC,OAAO;YACP,KAAK;QACP,GAAG,IAAI,EAAE,MAAM,kBAAkB,CAAC,EAAE,EAAE;YACpC,MAAM;YACN,QAAQ;QACV,GAAG,GAAG,EAAE,IAAI,kBAAkB,CAAC,EAAE,EAAE;YACjC,MAAM;YACN,QAAQ;QACV,IAAI;IACN;IAEA,OAAO,CAAC,yBACN,8OAAC,qJAAA,CAAA,2BAAwB;;;;6BAEzB,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAsB;;;;;;YAGnC,uCACC,8OAAC,sKAAA,CAAA,oBAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,eAAe;;;;;;YAIlB,SAAS,KAAK,CAAC,MAAM,CACpB,CAAC,UACC,QAAQ,MAAM,KAAK,cAAc,CAAC,kBAAkB,UACtD,MAAM,GAAG,kBACT,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CACZ,MAAM,CACL,CAAC,UACC,QAAQ,MAAM,KAAK,cAAc,CAAC,kBAAkB,UAEvD,GAAG,CAAC,CAAC,wBACJ,8OAAC;wBAEC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,UAAU;;;;;;sEAErB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EACE,WAAW,QAAQ,UAAU,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAKxD,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,YAAY,iBACnB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,QAAQ,YAAY;oDAC1B,QAAO;oDACP,KAAI;oDACJ,WAAU;8DACX;;;;;yEAID,8OAAC;oDACC,QAAQ;oDACR,WAAU;8DACX;;;;;;8DAIH,8OAAC;oDACC,SAAS;wDACP,yBAAyB;4DACvB,SAAS,QAAQ,iBAAiB;4DAClC,YACE,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4DAC3C,WACE,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4DAC3C,qBACE,QAAQ,yBAAyB,IAAI;wDACzC;wDACA,mBAAmB;oDACrB;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KACE,QAAQ,yBAAyB,IACjC,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAChB,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,IACzC,QAAQ,cAAc,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4CAG7C,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,cAAc,IAAI;;;;;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;uBA/ExC,QAAQ,EAAE;;;;;;;;;uBAsFrB,wBACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;qCAG3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C"}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/feedback-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\n\r\nimport { <PERSON><PERSON>, DialogContent, DialogTitle } from \"@/app/components/ui/dialog\";\r\nimport { Video, Clock, ArrowLeft } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\ninterface FeedbackDialogProps {\r\n  feedback: {\r\n    session_id: number;\r\n    event_name: string;\r\n    start_time: string;\r\n    end_time: string;\r\n    instructor_name: string;\r\n    maxCharLimit?: number;\r\n    time?: string;\r\n  } | null;\r\n  onClose: () => void;\r\n  onSubmit: (data: {\r\n    rating: number;\r\n    received_expected_service: boolean;\r\n    comment: string;\r\n    additional_feedback: string;\r\n  }) => void;\r\n  isSubmitting: boolean;\r\n}\r\n\r\nexport const FeedbackDialog = ({\r\n  feedback,\r\n  onClose,\r\n  onSubmit,\r\n  isSubmitting,\r\n}: FeedbackDialogProps) => {\r\n  const [formData, setFormData] = useState({\r\n    serviceAsExpected: \"yes\",\r\n    rating: 0,\r\n    counsellorComments: \"\",\r\n    additionalFeedback: \"\",\r\n  });\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  const handleClose = () => {\r\n    setFormData({\r\n      serviceAsExpected: \"yes\",\r\n      rating: 0,\r\n      counsellorComments: \"\",\r\n      additionalFeedback: \"\",\r\n    });\r\n    setErrors({});\r\n    onClose();\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (formData.rating === 0) {\r\n      newErrors.rating = \"Please provide a rating\";\r\n    }\r\n    if (!formData.counsellorComments.trim()) {\r\n      newErrors.counsellorComments =\r\n        \"Please provide comments for the counsellor\";\r\n    }\r\n\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    onSubmit({\r\n      rating: formData.rating,\r\n      received_expected_service: formData.serviceAsExpected === \"yes\",\r\n      comment: formData.counsellorComments,\r\n      additional_feedback: formData.additionalFeedback,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Dialog open={!!feedback} onOpenChange={handleClose}>\r\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto rounded-2xl\">\r\n        <div className=\"bg-gray-50 p-4\">\r\n          <DialogTitle className=\"text-xl font-semibold\">Feedback</DialogTitle>\r\n        </div>\r\n        <div className=\"px-4 py-3 border-b bg-white\">\r\n          <p className=\"text-gray-900 text-base font-medium\">\r\n            Provide feedback for your session\r\n          </p>\r\n        </div>\r\n        <form onSubmit={handleSubmit} className=\"p-4 space-y-4\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"bg-gray-50 p-4 rounded-xl space-y-3\">\r\n              <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3\">\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"p-2.5 bg-purple-100 rounded-xl shrink-0\">\r\n                    <Video className=\"w-5 h-5 text-purple-600\" />\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"font-medium\">{feedback?.event_name}</h3>\r\n                    <p className=\"text-gray-500 text-sm\"></p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center gap-2 text-gray-900 font-medium shrink-0\">\r\n                  <Clock className=\"w-4 h-4\" />\r\n                  {feedback && (\r\n                    <span className=\"text-sm\">\r\n                      {new Date(feedback!.start_time).toLocaleDateString()}{\" \"}\r\n                      {new Date(feedback!.start_time).toLocaleTimeString([], {\r\n                        hour: \"2-digit\",\r\n                        minute: \"2-digit\",\r\n                      })}\r\n                      {\" - \"}\r\n                      {new Date(feedback!.end_time).toLocaleTimeString([], {\r\n                        hour: \"2-digit\",\r\n                        minute: \"2-digit\",\r\n                      })}\r\n                    </span>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-2\">\r\n                <Image\r\n                  src={generatePlaceholder(\r\n                    feedback?.instructor_name.trim().split(/s+/)[0],\r\n                    feedback?.instructor_name.trim().split(/\\s+/)[1]\r\n                  )}\r\n                  alt={feedback?.instructor_name || \"Instructor\"}\r\n                  width={32}\r\n                  height={32}\r\n                  className=\"rounded-full\"\r\n                />\r\n                <span className=\"text-sm\">{feedback?.instructor_name}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label className=\"text-sm font-medium block\">\r\n                Did you receive service as expected?\r\n              </label>\r\n              <div className=\"flex gap-6\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"service\"\r\n                    value=\"yes\"\r\n                    id=\"yes\"\r\n                    checked={formData.serviceAsExpected === \"yes\"}\r\n                    onChange={(e) =>\r\n                      setFormData({\r\n                        ...formData,\r\n                        serviceAsExpected: e.target.value,\r\n                      })\r\n                    }\r\n                    className=\"w-5 h-5 text-[#15194B] cursor-pointer\"\r\n                  />\r\n                  <label htmlFor=\"yes\" className=\"text-sm cursor-pointer\">\r\n                    Yes\r\n                  </label>\r\n                </div>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    name=\"service\"\r\n                    value=\"no\"\r\n                    id=\"no\"\r\n                    checked={formData.serviceAsExpected === \"no\"}\r\n                    onChange={(e) =>\r\n                      setFormData({\r\n                        ...formData,\r\n                        serviceAsExpected: e.target.value,\r\n                      })\r\n                    }\r\n                    className=\"w-5 h-5 text-[#15194B] cursor-pointer\"\r\n                  />\r\n                  <label htmlFor=\"no\" className=\"text-sm cursor-pointer\">\r\n                    No\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label className=\"text-sm font-medium block\">\r\n                Please rate your session\r\n              </label>\r\n              <div className=\"flex gap-2\">\r\n                {[1, 2, 3, 4, 5].map((star) => (\r\n                  <button\r\n                    key={star}\r\n                    type=\"button\"\r\n                    onClick={() => setFormData({ ...formData, rating: star })}\r\n                    className={`p-1 ${\r\n                      formData.rating >= star\r\n                        ? \"text-yellow-400\"\r\n                        : \"text-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <svg\r\n                      className=\"w-8 h-8\"\r\n                      fill=\"currentColor\"\r\n                      viewBox=\"0 0 20 20\"\r\n                    >\r\n                      <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n                    </svg>\r\n                  </button>\r\n                ))}\r\n              </div>\r\n              {errors.rating && (\r\n                <p className=\"text-red-500 text-sm\">{errors.rating}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label className=\"text-sm font-medium block\">\r\n                Would you like to provide any comments for the counsellor?\r\n              </label>\r\n              <div className=\"relative\">\r\n                <textarea\r\n                  value={formData.counsellorComments}\r\n                  onChange={(e) =>\r\n                    setFormData({\r\n                      ...formData,\r\n                      counsellorComments: e.target.value,\r\n                    })\r\n                  }\r\n                  maxLength={feedback?.maxCharLimit}\r\n                  className=\"w-full min-h-[100px] md:min-h-[160px] p-2 md:p-3 text-sm rounded-lg border focus:outline-none focus:ring-2 focus:ring-gray-200\"\r\n                  placeholder=\"Enter your comments here...\"\r\n                />\r\n                <span className=\"absolute bottom-1.5 md:bottom-2 right-1.5 md:right-2 text-xs md:text-sm text-gray-400\">\r\n                  {formData.counsellorComments.length}/{feedback?.maxCharLimit}\r\n                </span>\r\n              </div>\r\n              {errors.counsellorComments && (\r\n                <p className=\"text-red-500 text-xs md:text-sm\">\r\n                  {errors.counsellorComments}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <label className=\"text-sm font-medium block\">\r\n                Would you like to share any other feedback? (Optional)\r\n              </label>\r\n              <div className=\"relative\">\r\n                <textarea\r\n                  value={formData.additionalFeedback}\r\n                  onChange={(e) =>\r\n                    setFormData({\r\n                      ...formData,\r\n                      additionalFeedback: e.target.value,\r\n                    })\r\n                  }\r\n                  maxLength={feedback?.maxCharLimit}\r\n                  className=\"w-full min-h-[100px] md:min-h-[160px] p-2 md:p-3 text-sm rounded-lg border focus:outline-none focus:ring-2 focus:ring-gray-200\"\r\n                  placeholder=\"Enter additional feedback...\"\r\n                />\r\n                <span className=\"absolute bottom-1.5 md:bottom-2 right-1.5 md:right-2 text-xs md:text-sm text-gray-400\">\r\n                  {formData.additionalFeedback.length}/{feedback?.maxCharLimit}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-end gap-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleClose}\r\n              className=\"px-4 py-2 border rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2 text-sm\"\r\n            >\r\n              <ArrowLeft className=\"w-4 h-4\" />\r\n              Back\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className=\"px-4 py-2 bg-[#15194B] text-white rounded-xl font-medium hover:bg-gray-900 transition-colors text-sm disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\r\n            >\r\n              {isSubmitting ? (\r\n                <>\r\n                  <svg\r\n                    className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                  >\r\n                    <circle\r\n                      className=\"opacity-25\"\r\n                      cx=\"12\"\r\n                      cy=\"12\"\r\n                      r=\"10\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"4\"\r\n                    ></circle>\r\n                    <path\r\n                      className=\"opacity-75\"\r\n                      fill=\"currentColor\"\r\n                      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n                    ></path>\r\n                  </svg>\r\n                  Submitting...\r\n                </>\r\n              ) : (\r\n                \"Continue\"\r\n              )}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AACA;AACA;AAHA;AAAA;AAAA;AALA;;;;;;;AA8BO,MAAM,iBAAiB,CAAC,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,YAAY,EACQ;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,mBAAmB;QACnB,QAAQ;QACR,oBAAoB;QACpB,oBAAoB;IACtB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,cAAc;QAClB,YAAY;YACV,mBAAmB;YACnB,QAAQ;YACR,oBAAoB;YACpB,oBAAoB;QACtB;QACA,UAAU,CAAC;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,YAAoC,CAAC;QAE3C,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,UAAU,MAAM,GAAG;QACrB;QACA,IAAI,CAAC,SAAS,kBAAkB,CAAC,IAAI,IAAI;YACvC,UAAU,kBAAkB,GAC1B;QACJ;QAEA,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,UAAU;YACV;QACF;QAEA,SAAS;YACP,QAAQ,SAAS,MAAM;YACvB,2BAA2B,SAAS,iBAAiB,KAAK;YAC1D,SAAS,SAAS,kBAAkB;YACpC,qBAAqB,SAAS,kBAAkB;QAClD;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM,CAAC,CAAC;QAAU,cAAc;kBACtC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAwB;;;;;;;;;;;8BAEjD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;8BAIrD,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAe,UAAU;;;;;;8EACvC,8OAAC;oEAAE,WAAU;;;;;;;;;;;;;;;;;;8DAGjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,0BACC,8OAAC;4DAAK,WAAU;;gEACb,IAAI,KAAK,SAAU,UAAU,EAAE,kBAAkB;gEAAI;gEACrD,IAAI,KAAK,SAAU,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;oEACrD,MAAM;oEACN,QAAQ;gEACV;gEACC;gEACA,IAAI,KAAK,SAAU,QAAQ,EAAE,kBAAkB,CAAC,EAAE,EAAE;oEACnD,MAAM;oEACN,QAAQ;gEACV;;;;;;;;;;;;;;;;;;;sDAMR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EACrB,UAAU,gBAAgB,OAAO,MAAM,KAAK,CAAC,EAAE,EAC/C,UAAU,gBAAgB,OAAO,MAAM,MAAM,CAAC,EAAE;oDAElD,KAAK,UAAU,mBAAmB;oDAClC,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAW,UAAU;;;;;;;;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA4B;;;;;;sDAG7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,IAAG;4DACH,SAAS,SAAS,iBAAiB,KAAK;4DACxC,UAAU,CAAC,IACT,YAAY;oEACV,GAAG,QAAQ;oEACX,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACnC;4DAEF,WAAU;;;;;;sEAEZ,8OAAC;4DAAM,SAAQ;4DAAM,WAAU;sEAAyB;;;;;;;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,IAAG;4DACH,SAAS,SAAS,iBAAiB,KAAK;4DACxC,UAAU,CAAC,IACT,YAAY;oEACV,GAAG,QAAQ;oEACX,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACnC;4DAEF,WAAU;;;;;;sEAEZ,8OAAC;4DAAM,SAAQ;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA4B;;;;;;sDAG7C,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;oDAEC,MAAK;oDACL,SAAS,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,QAAQ;wDAAK;oDACvD,WAAW,CAAC,IAAI,EACd,SAAS,MAAM,IAAI,OACf,oBACA,iBACJ;8DAEF,cAAA,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;kEAER,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;mDAdL;;;;;;;;;;wCAmBV,OAAO,MAAM,kBACZ,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,MAAM;;;;;;;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA4B;;;;;;sDAG7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IACT,YAAY;4DACV,GAAG,QAAQ;4DACX,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACpC;oDAEF,WAAW,UAAU;oDACrB,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDAAK,WAAU;;wDACb,SAAS,kBAAkB,CAAC,MAAM;wDAAC;wDAAE,UAAU;;;;;;;;;;;;;wCAGnD,OAAO,kBAAkB,kBACxB,8OAAC;4CAAE,WAAU;sDACV,OAAO,kBAAkB;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA4B;;;;;;sDAG7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IACT,YAAY;4DACV,GAAG,QAAQ;4DACX,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACpC;oDAEF,WAAW,UAAU;oDACrB,WAAU;oDACV,aAAY;;;;;;8DAEd,8OAAC;oDAAK,WAAU;;wDACb,SAAS,kBAAkB,CAAC,MAAM;wDAAC;wDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;sCAMxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGnC,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,MAAK;gDACL,SAAQ;;kEAER,8OAAC;wDACC,WAAU;wDACV,IAAG;wDACH,IAAG;wDACH,GAAE;wDACF,QAAO;wDACP,aAAY;;;;;;kEAEd,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,GAAE;;;;;;;;;;;;4CAEA;;uDAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB"}}, {"offset": {"line": 1962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1968, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/pending-feedback.tsx"], "sourcesContent": ["import { MessageSquare, Video } from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { FeedbackDialog } from \"./feedback-dialog\";\r\nimport { SuccessDialog } from \"./success-dialog\";\r\nimport { useOverview } from \"@hooks/student/useOverview\";\r\nimport { toast } from \"react-toastify\";\r\nimport { PendingFeedbacksSkeleton } from \"./skeleton\";\r\n\r\ninterface PendingFeedbackSession {\r\n  session_id: number;\r\n  event_name: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  instructor_name: string;\r\n}\r\n\r\nexport const PendingFeedbacks = () => {\r\n  const { pendingFeedbacks, submitFeedback } = useOverview();\r\n  const [selectedFeedback, setSelectedFeedback] =\r\n    useState<PendingFeedbackSession | null>(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (pendingFeedbacks !== undefined) {\r\n      setLoading(false);\r\n    }\r\n  }, [pendingFeedbacks]);\r\n\r\n  const handleSubmitFeedback = async (data: {\r\n    rating: number;\r\n    received_expected_service: boolean;\r\n    comment: string;\r\n    additional_feedback: string;\r\n  }) => {\r\n    try {\r\n      if (!selectedFeedback) return;\r\n      setIsSubmitting(true);\r\n      toast.loading(\"Submitting feedback...\");\r\n      await submitFeedback(selectedFeedback.session_id, data);\r\n\r\n      setIsSubmitting(false);\r\n      setSelectedFeedback(null);\r\n      toast.dismiss();\r\n      toast.success(\"Feedback submitted successfully!\");\r\n    } catch (error: any) {\r\n      toast.error(error.message || \"Failed to submit feedback\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {!pendingFeedbacks || loading ? (\r\n        <PendingFeedbacksSkeleton />\r\n      ) : (\r\n        <div className=\"rounded-2xl border flex flex-col gap-6 md:p-6 p-4\">\r\n          <h3 className=\"text-xl font-medium\">Pending Feedback</h3>\r\n          {pendingFeedbacks.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {pendingFeedbacks.map((feedback) => (\r\n                <div\r\n                  key={feedback.session_id}\r\n                  onClick={() => setSelectedFeedback(feedback)}\r\n                  className=\"p-4 bg-gray-50 border rounded-2xl cursor-pointer hover:border-gray-300 hover:shadow-sm transition-all duration-200\"\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div className=\"p-2 bg-green-200 rounded-xl\">\r\n                      <Video className=\"w-5 h-5 text-green-800\" />\r\n                    </div>\r\n                    <div>\r\n                      <h3 className=\"text-lg font-medium\">\r\n                        {feedback.event_name}\r\n                      </h3>\r\n                      <p className=\"text-gray-500\">\r\n                        {new Date(feedback.start_time).toLocaleTimeString([], {\r\n                          hour: \"2-digit\",\r\n                          minute: \"2-digit\",\r\n                        })}\r\n                        • Instructor: {feedback.instructor_name}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col items-center justify-center py-6 text-center\">\r\n              <MessageSquare className=\"w-10 h-10 text-gray-400 mb-2\" />\r\n              <p className=\"text-gray-600 font-medium\">No pending feedback</p>\r\n              <p className=\"text-gray-500 text-sm\">\r\n                All feedback has been submitted\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      <FeedbackDialog\r\n        feedback={selectedFeedback}\r\n        onClose={() => setSelectedFeedback(null)}\r\n        onSubmit={handleSubmitFeedback}\r\n        isSubmitting={isSubmitting}\r\n      />\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;AANA;AAAA;;;;;;;;AAgBO,MAAM,mBAAmB;IAC9B,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB,WAAW;YAClC,WAAW;QACb;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,uBAAuB,OAAO;QAMlC,IAAI;YACF,IAAI,CAAC,kBAAkB;YACvB,gBAAgB;YAChB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,MAAM,eAAe,iBAAiB,UAAU,EAAE;YAElD,gBAAgB;YAChB,oBAAoB;YACpB,mJAAA,CAAA,QAAK,CAAC,OAAO;YACb,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF;IAEA,qBACE;;YACG,CAAC,oBAAoB,wBACpB,8OAAC,qJAAA,CAAA,2BAAwB;;;;qCAEzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsB;;;;;;oBACnC,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,SAAS,UAAU;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;;wDACV,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB,CAAC,EAAE,EAAE;4DACpD,MAAM;4DACN,QAAQ;wDACV;wDAAG;wDACY,SAAS,eAAe;;;;;;;;;;;;;;;;;;;+BAjBxC,SAAS,UAAU;;;;;;;;;6CAyB9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC,+JAAA,CAAA,iBAAc;gBACb,UAAU;gBACV,SAAS,IAAM,oBAAoB;gBACnC,UAAU;gBACV,cAAc;;;;;;;;AAItB"}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/head-wrapper.tsx"], "sourcesContent": ["import Head from \"next/head\";\r\n\r\nexport default function HeadWrapper({\r\n  title,\r\n  description,\r\n}: {\r\n  title: string;\r\n  description?: string;\r\n}) {\r\n  return (\r\n    <Head>\r\n      <title>{`${title || \"\"} | Student's Portal | AdmitPath`}</title>\r\n      <meta\r\n        name=\"description\"\r\n        content={`Student Dashboard Portal for accessing and managing all your sessions and resources. ${\r\n          description || \"\"\r\n        } | AdmitPath`}\r\n      />\r\n    </Head>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,YAAY,EAClC,KAAK,EACL,WAAW,EAIZ;IACC,qBACE,8OAAC,oKAAA,CAAA,UAAI;;0BACH,8OAAC;0BAAO,GAAG,SAAS,GAAG,+BAA+B,CAAC;;;;;;0BACvD,8OAAC;gBACC,MAAK;gBACL,SAAS,CAAC,qFAAqF,EAC7F,eAAe,GAChB,YAAY,CAAC;;;;;;;;;;;;AAItB"}}, {"offset": {"line": 2186, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/overview/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useOverview } from \"@hooks/student/useOverview\";\r\nimport { StatsCards } from \"@components/student/overview/stats-card\";\r\nimport { UpcomingSessions } from \"@components/student/overview/upcoming-sessions\";\r\nimport { PendingFeedbacks } from \"@components/student/overview/pending-feedback\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useSessions } from \"@/app/hooks/student/useSessions\";\r\nimport HeadWrapper from \"../head-wrapper\";\r\n\r\nexport default function Overview() {\r\n  const { fetchStats, fetchPendingFeedbacks } = useOverview();\r\n  const { fetchSessions } = useSessions();\r\n\r\n  useEffect(() => {\r\n    const loadDashboardData = async () => {\r\n      try {\r\n        await Promise.all([\r\n          fetchStats(),\r\n          fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone),\r\n          fetchPendingFeedbacks(),\r\n        ]);\r\n      } catch (error: any) {\r\n        toast.error(error.message || \"Failed to load dashboard data\");\r\n      }\r\n    };\r\n\r\n    loadDashboardData();\r\n  }, [fetchStats, fetchSessions, fetchPendingFeedbacks]);\r\n\r\n  return (\r\n    <>\r\n      <HeadWrapper title=\"Dashboard\" />\r\n      <div className=\"bg-white flex flex-col gap-6 rounded-2xl border sm:p-6 p-4\">\r\n        <StatsCards />\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-6\">\r\n          <div className=\"lg:col-span-7\">\r\n            <UpcomingSessions />\r\n          </div>\r\n          <div className=\"lg:col-span-5 flex flex-col gap-6\">\r\n            <PendingFeedbacks />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,QAAQ,GAAG,CAAC;oBAChB;oBACA,cAAc,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;oBAC9D;iBACD;YACH,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;QACF;QAEA;IACF,GAAG;QAAC;QAAY;QAAe;KAAsB;IAErD,qBACE;;0BACE,8OAAC,gJAAA,CAAA,UAAW;gBAAC,OAAM;;;;;;0BACnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0JAAA,CAAA,aAAU;;;;;kCACX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iKAAA,CAAA,mBAAgB;;;;;;;;;;0CAEnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gKAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAM7B"}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/student/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Overview from \"@/app/components/student/overview\";\r\n\r\nexport default function OverviewPage() {\r\n  return <Overview />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,kJAAA,CAAA,UAAQ;;;;;AAClB"}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}