{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAGW;AALX;;AAIA,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/constants/socialButtons.ts"], "sourcesContent": ["export const socialButtons = [\r\n  {\r\n    label: \"Google\",\r\n    altText: \"Google sign in\",\r\n    provider: \"google\",\r\n  },\r\n  {\r\n    label: \"LinkedIn\",\r\n    altText: \"LinkedIn sign in\",\r\n    provider: \"linkedin\",\r\n  },\r\n  // Apple sign in temporarily disabled\r\n  // {\r\n  //   label: \"Apple\",\r\n  //   altText: \"Apple sign in\",\r\n  //   provider: \"apple\",\r\n  // },\r\n] as const;\r\n\r\nexport enum SOCIAL_PROVIDERS {\r\n  GOOGLE = \"google\",\r\n  LINKEDIN = \"linkedin\",\r\n  // APPLE = \"apple\",\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,MAAM,gBAAgB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;IACZ;CAOD;AAEM,IAAA,AAAK,0CAAA;;;WAAA"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AAEgB;;AAAhB,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM;IACN,MAAM;IACN,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,uHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/socialButtons/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { FaA<PERSON>le, FaLinkedin } from \"react-icons/fa\";\r\nimport { SOCIAL_PROVIDERS } from \"@constants/socialButtons\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\n\r\ninterface SocialButtonProps {\r\n  label: string;\r\n  altText: string;\r\n  type: \"google\" | \"linkedin\" | \"apple\";\r\n  userType?: \"student\" | \"counselor\";\r\n  isLogin?: boolean;\r\n}\r\n\r\nexport const SocialButton: FC<SocialButtonProps> = ({\r\n  label,\r\n  altText,\r\n  type,\r\n  userType,\r\n  isLogin = false,\r\n}) => {\r\n  const { googleAuth, initiateLinkedInAuth } = useAuth();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleClick = async () => {\r\n    setLoading(true);\r\n    try {\r\n      switch (type) {\r\n        case SOCIAL_PROVIDERS.GOOGLE:\r\n          const { google } = window as any;\r\n          const auth2 = google.accounts.oauth2;\r\n\r\n          const client = auth2.initTokenClient({\r\n            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,\r\n            scope: \"openid email profile\",\r\n            callback: async (response: any) => {\r\n              try {\r\n                // Only pass userType for signup\r\n                await googleAuth(\r\n                  response.access_token,\r\n                  isLogin ? undefined : userType\r\n                );\r\n              } catch (error: any) {\r\n                if (process.env.NODE_ENV === \"development\") {\r\n                  console.error(\"Google auth error:\", error);\r\n                }\r\n\r\n                if (\r\n                  error.response?.data?.detail.includes(\"already registered\")\r\n                ) {\r\n                  toast.error(error.response?.data?.detail, {\r\n                    toastId: \"auth-error\",\r\n                  });\r\n                } else if (\r\n                  error.response?.data?.detail ===\r\n                  \"user_type is required for registration\"\r\n                ) {\r\n                  toast.error(\r\n                    \"Please use the signup page to register as either a student or counselor.\",\r\n                    { toastId: \"auth-error\" }\r\n                  );\r\n                } else {\r\n                  toast.error(\r\n                    \"Failed to sign in with Google. Please try again.\",\r\n                    { toastId: \"auth-error\" }\r\n                  );\r\n                }\r\n              } finally {\r\n                setLoading(false);\r\n              }\r\n            },\r\n          });\r\n\r\n          client.requestAccessToken();\r\n          break;\r\n\r\n        case SOCIAL_PROVIDERS.LINKEDIN:\r\n          try {\r\n            // Only pass userType for signup\r\n            initiateLinkedInAuth(isLogin ? undefined : userType);\r\n          } catch (error: any) {\r\n            if (process.env.NODE_ENV === \"development\") {\r\n              console.error(\"LinkedIn auth error:\", error);\r\n            }\r\n\r\n            if (error.response?.data?.detail.includes(\"already registered\")) {\r\n              toast.error(error.response?.data?.detail, {\r\n                toastId: \"auth-error\",\r\n              });\r\n            } else if (\r\n              error.response?.data?.detail ===\r\n              \"user_type is required for registration\"\r\n            ) {\r\n              toast.error(\r\n                \"Please use the signup page to register as either a student or counselor.\",\r\n                { toastId: \"auth-error\" }\r\n              );\r\n            } else {\r\n              toast.error(\r\n                \"Failed to sign in with LinkedIn. Please try again.\",\r\n                { toastId: \"auth-error\" }\r\n              );\r\n            }\r\n          }\r\n          setLoading(false);\r\n          break;\r\n\r\n        // case SOCIAL_PROVIDERS.APPLE:\r\n        //   if (process.env.NODE_ENV === \"development\") {\r\n        //     console.log(\"Apple Sign In not implemented yet\");\r\n        //   }\r\n        //   toast.info(\"Apple Sign In coming soon!\", { toastId: \"apple-signin\" });\r\n        //   setLoading(false);\r\n        //   break;\r\n      }\r\n    } catch (error) {\r\n      if (process.env.NODE_ENV === \"development\") {\r\n        console.error(\"Social auth error:\", error);\r\n      }\r\n      toast.error(\"Something went wrong. Please try again.\", {\r\n        toastId: \"auth-error\",\r\n      });\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getIcon = (value: string) => {\r\n    switch (value) {\r\n      case SOCIAL_PROVIDERS.LINKEDIN:\r\n        return (\r\n          <FaLinkedin className=\"w-5 h-5 text-blue-600\" aria-hidden=\"true\" />\r\n        );\r\n      // case SOCIAL_PROVIDERS.APPLE:\r\n      //   return <FaApple className=\"w-5 h-5 text-gray-800\" aria-hidden=\"true\" />;\r\n      case SOCIAL_PROVIDERS.GOOGLE:\r\n        return (\r\n          <span className=\"w-5 h-5\">\r\n            <img src=\"/svgs/googleIcon.svg\" alt=\"Icon\" />\r\n          </span>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <button\r\n      type=\"button\"\r\n      className=\"flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n      aria-label={altText}\r\n      onClick={handleClick}\r\n      disabled={loading}\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        {getIcon(type)}\r\n        <span>{loading ? \"Loading...\" : label}</span>\r\n      </div>\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AA6BuB;AAjCvB;;;AAHA;;;;;;AAiBO,MAAM,eAAsC,CAAC,EAClD,KAAK,EACL,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,UAAU,KAAK,EAChB;;IACC,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,OAAQ;gBACN,KAAK,oIAAA,CAAA,mBAAgB,CAAC,MAAM;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;oBACnB,MAAM,QAAQ,OAAO,QAAQ,CAAC,MAAM;oBAEpC,MAAM,SAAS,MAAM,eAAe,CAAC;wBACnC,SAAS;wBACT,OAAO;wBACP,UAAU,OAAO;4BACf,IAAI;gCACF,gCAAgC;gCAChC,MAAM,WACJ,SAAS,YAAY,EACrB,UAAU,YAAY;4BAE1B,EAAE,OAAO,OAAY;gCACnB,wCAA4C;oCAC1C,QAAQ,KAAK,CAAC,sBAAsB;gCACtC;gCAEA,IACE,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,uBACtC;oCACA,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,QAAQ;wCACxC,SAAS;oCACX;gCACF,OAAO,IACL,MAAM,QAAQ,EAAE,MAAM,WACtB,0CACA;oCACA,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,4EACA;wCAAE,SAAS;oCAAa;gCAE5B,OAAO;oCACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,oDACA;wCAAE,SAAS;oCAAa;gCAE5B;4BACF,SAAU;gCACR,WAAW;4BACb;wBACF;oBACF;oBAEA,OAAO,kBAAkB;oBACzB;gBAEF,KAAK,oIAAA,CAAA,mBAAgB,CAAC,QAAQ;oBAC5B,IAAI;wBACF,gCAAgC;wBAChC,qBAAqB,UAAU,YAAY;oBAC7C,EAAE,OAAO,OAAY;wBACnB,wCAA4C;4BAC1C,QAAQ,KAAK,CAAC,wBAAwB;wBACxC;wBAEA,IAAI,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,uBAAuB;4BAC/D,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,QAAQ;gCACxC,SAAS;4BACX;wBACF,OAAO,IACL,MAAM,QAAQ,EAAE,MAAM,WACtB,0CACA;4BACA,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,4EACA;gCAAE,SAAS;4BAAa;wBAE5B,OAAO;4BACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CACT,sDACA;gCAAE,SAAS;4BAAa;wBAE5B;oBACF;oBACA,WAAW;oBACX;YASJ;QACF,EAAE,OAAO,OAAO;YACd,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;YACA,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;gBACrD,SAAS;YACX;YACA,WAAW;QACb;IACF;IAEA,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK,oIAAA,CAAA,mBAAgB,CAAC,QAAQ;gBAC5B,qBACE,6LAAC,iJAAA,CAAA,aAAU;oBAAC,WAAU;oBAAwB,eAAY;;;;;;YAE9D,+BAA+B;YAC/B,6EAA6E;YAC7E,KAAK,oIAAA,CAAA,mBAAgB,CAAC,MAAM;gBAC1B,qBACE,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,KAAI;wBAAuB,KAAI;;;;;;;;;;;YAG1C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,WAAU;QACV,cAAY;QACZ,SAAS;QACT,UAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;gBACZ,QAAQ;8BACT,6LAAC;8BAAM,UAAU,eAAe;;;;;;;;;;;;;;;;;AAIxC;GAjJa;;QAOkC,0HAAA,CAAA,UAAO;;;KAPzC"}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/layout.tsx"], "sourcesContent": ["import type { FC, ReactNode } from \"react\";\r\nimport { SocialButton } from \"@components/common/socialButtons\";\r\nimport { socialButtons } from \"@constants/socialButtons\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\ninterface LayoutProps {\r\n    header: ReactNode;\r\n    formContent: ReactNode;\r\n    isLogin: boolean;\r\n    userType?: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const Layout: FC<LayoutProps> = ({\r\n    header,\r\n    formContent,\r\n    isLogin,\r\n    userType = \"student\",\r\n}) => {\r\n    return (\r\n        <div className=\"min-h-screen flex flex-col lg:flex-row overflow-hidden\">\r\n            <div className=\"w-full lg:w-1/2 flex flex-col bg-stone-100 min-h-screen overflow-y-auto\">\r\n                <header className=\"p-4 sm:py-6 sm:px-8 flex items-center\">\r\n                    {/* Logo */}\r\n                    <Link href={\"/\"}>\r\n                        <Image\r\n                            src=\"/icons/logo.png\"\r\n                            alt=\"AdmitPath Logo\"\r\n                            width={100}\r\n                            height={100}\r\n                            className=\"w-[4.5rem]\"\r\n                            priority\r\n                        />\r\n                    </Link>\r\n                </header>\r\n\r\n                <div className=\"flex-1 flex justify-center items-center py-8 px-4\">\r\n                    <div className=\"w-full max-w-lg bg-white rounded-xl p-6 sm:p-8 mx-auto shadow-sm\">\r\n                        {header}\r\n                        <div className=\"flex flex-col sm:flex-row gap-3 w-full my-6\">\r\n                            {socialButtons.map((button) => (\r\n                                <SocialButton\r\n                                    key={button.provider}\r\n                                    type={button.provider}\r\n                                    label={button.label}\r\n                                    altText={button.altText}\r\n                                    userType={userType}\r\n                                    isLogin={isLogin}\r\n                                />\r\n                            ))}\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center gap-4 my-6\">\r\n                            <hr className=\"flex-1 border-gray-200\" />\r\n                            <span className=\"text-gray-500 text-xs sm:text-sm whitespace-nowrap\">\r\n                                or continue with email\r\n                            </span>\r\n                            <hr className=\"flex-1 border-gray-200\" />\r\n                        </div>\r\n\r\n                        {formContent}\r\n\r\n                        {!isLogin && (\r\n                            <div className=\"mt-6 text-center\">\r\n                                {userType === \"student\" ? (\r\n                                    <Link\r\n                                        href=\"/auth/signup/counselor\"\r\n                                        className=\"text-blue-600 hover:text-blue-700 font-medium\"\r\n                                    >\r\n                                        Sign up as a Counselor\r\n                                    </Link>\r\n                                ) : (\r\n                                    <Link\r\n                                        href=\"/auth/signup\"\r\n                                        className=\"text-blue-600 hover:text-blue-700 font-medium\"\r\n                                    >\r\n                                        Sign up as a Student\r\n                                    </Link>\r\n                                )}\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"hidden lg:block lg:w-1/2 relative\">\r\n                <div className=\"absolute inset-0 bg-black\">\r\n                    <img\r\n                        src=\"/svgs/authCover.svg\"\r\n                        alt=\"Counselor profile\"\r\n                        className=\"w-full h-full object-cover\"\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AASO,MAAM,SAA0B,CAAC,EACpC,MAAM,EACN,WAAW,EACX,OAAO,EACP,WAAW,SAAS,EACvB;IACG,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAO,WAAU;kCAEd,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;sCACR,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACF,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BAAI,WAAU;;gCACV;8CACD,6LAAC;oCAAI,WAAU;8CACV,oIAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,uBAChB,6LAAC,yJAAA,CAAA,eAAY;4CAET,MAAM,OAAO,QAAQ;4CACrB,OAAO,OAAO,KAAK;4CACnB,SAAS,OAAO,OAAO;4CACvB,UAAU;4CACV,SAAS;2CALJ,OAAO,QAAQ;;;;;;;;;;8CAUhC,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAG,WAAU;;;;;;sDACd,6LAAC;4CAAK,WAAU;sDAAqD;;;;;;sDAGrE,6LAAC;4CAAG,WAAU;;;;;;;;;;;;gCAGjB;gCAEA,CAAC,yBACE,6LAAC;oCAAI,WAAU;8CACV,aAAa,0BACV,6LAAC,+JAAA,CAAA,UAAI;wCACD,MAAK;wCACL,WAAU;kDACb;;;;;6DAID,6LAAC,+JAAA,CAAA,UAAI;wCACD,MAAK;wCACL,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzB,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBACG,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAnFa"}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,6LAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,6LAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,6LAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,6LAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX;KAlCa"}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/button/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"buttonBase\": \"index-module__Mmy9Oa__buttonBase\",\n  \"buttonContent\": \"index-module__Mmy9Oa__buttonContent\",\n  \"buttonPrimary\": \"index-module__Mmy9Oa__buttonPrimary\",\n  \"buttonSecondary\": \"index-module__Mmy9Oa__buttonSecondary\",\n  \"icon\": \"index-module__Mmy9Oa__icon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/button/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface ButtonProps {\r\n  variant: \"primary\" | \"secondary\";\r\n  children: React.ReactNode;\r\n  onClick?: (e: any) => void;\r\n  type?: \"button\" | \"submit\" | \"reset\";\r\n  disabled?: boolean;\r\n  icon?: React.ReactNode;\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({\r\n  variant,\r\n  children,\r\n  onClick,\r\n  type = \"button\",\r\n  disabled = false,\r\n  icon,\r\n}) => {\r\n  const buttonClasses = `${styles.buttonBase} ${\r\n    variant === \"primary\" ? styles.buttonPrimary : styles.buttonSecondary\r\n  }`;\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      className={buttonClasses}\r\n      disabled={disabled}\r\n    >\r\n      <span className={styles.buttonContent}>\r\n        {children}\r\n        {icon && <span className={styles.icon}>{icon}</span>}\r\n      </span>\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAcO,MAAM,SAAgC,CAAC,EAC5C,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,IAAI,EACL;IACC,MAAM,gBAAgB,GAAG,6JAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC1C,YAAY,YAAY,6JAAA,CAAA,UAAM,CAAC,aAAa,GAAG,6JAAA,CAAA,UAAM,CAAC,eAAe,EACrE;IAEF,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,WAAW;QACX,UAAU;kBAEV,cAAA,6LAAC;YAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,aAAa;;gBAClC;gBACA,sBAAQ,6LAAC;oBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;8BAAG;;;;;;;;;;;;;;;;;AAIhD;KAzBa"}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/passwordInputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"eyeIcon\": \"index-module__zj4Tga__eyeIcon\",\n  \"inputIcon\": \"index-module__zj4Tga__inputIcon\",\n  \"passwordContainer\": \"index-module__zj4Tga__passwordContainer\",\n  \"passwordTooltip\": \"index-module__zj4Tga__passwordTooltip\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/passwordInputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, useState } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';\r\nimport styles from './index.module.css';\r\nimport { InputField } from '../inputField';\r\n\r\ninterface PasswordInputFieldProps {\r\n  label: string;\r\n  name: string;\r\n  value: string;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  error?: string;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  showPasswordTooltip?: boolean;\r\n}\r\n\r\nexport const PasswordInputField: FC<PasswordInputFieldProps> = ({\r\n  label,\r\n  showPasswordTooltip = false,\r\n  ...props\r\n}) => {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showTooltip, setShowTooltip] = useState(false);\r\n\r\n  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);\r\n\r\n  return (\r\n    <div className={styles.passwordContainer}>\r\n      <InputField\r\n        {...props}\r\n        type={showPassword ? 'text' : 'password'}\r\n        label={label}\r\n        onFocus={() => setShowTooltip(true)}\r\n        onBlur={() => setShowTooltip(false)}\r\n      />\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={togglePasswordVisibility}\r\n        className={styles.inputIcon}\r\n        aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n      >\r\n        <FontAwesomeIcon\r\n          icon={showPassword ? faEyeSlash : faEye}\r\n          className={styles.eyeIcon}\r\n        />\r\n      </button>\r\n\r\n      {showPasswordTooltip && showTooltip && (\r\n        <div className={styles.passwordTooltip}>\r\n          <ul>\r\n            <li>Minimum 8 characters</li>\r\n            <li>At least 1 number</li>\r\n            <li>At least 1 uppercase letter</li>\r\n            <li>At least 1 symbol (!@#$%^&*)</li>\r\n          </ul>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;;;AAJA;;;;;;AAmBO,MAAM,qBAAkD,CAAC,EAC9D,KAAK,EACL,sBAAsB,KAAK,EAC3B,GAAG,OACJ;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,2BAA2B,IAAM,gBAAgB,CAAC,OAAS,CAAC;IAElE,qBACE,6LAAC;QAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,iBAAiB;;0BACtC,6LAAC,sJAAA,CAAA,aAAU;gBACR,GAAG,KAAK;gBACT,MAAM,eAAe,SAAS;gBAC9B,OAAO;gBACP,SAAS,IAAM,eAAe;gBAC9B,QAAQ,IAAM,eAAe;;;;;;0BAG/B,6LAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAW,yKAAA,CAAA,UAAM,CAAC,SAAS;gBAC3B,cAAY,eAAe,kBAAkB;0BAE7C,cAAA,6LAAC,uKAAA,CAAA,kBAAe;oBACd,MAAM,eAAe,2KAAA,CAAA,aAAU,GAAG,2KAAA,CAAA,QAAK;oBACvC,WAAW,yKAAA,CAAA,UAAM,CAAC,OAAO;;;;;;;;;;;YAI5B,uBAAuB,6BACtB,6LAAC;gBAAI,WAAW,yKAAA,CAAA,UAAM,CAAC,eAAe;0BACpC,cAAA,6LAAC;;sCACC,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAG;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GA5Ca;KAAA"}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/login/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC, useState } from \"react\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { Button } from \"@components/common/button\";\r\nimport { FaArrowRight } from \"react-icons/fa\";\r\nimport { PasswordInputField } from \"@components/common/passwordInputField\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\n\r\ninterface FormErrors {\r\n  email?: string;\r\n  password?: string;\r\n}\r\n\r\nexport interface FormProps {\r\n  handleSubmit: (data: { email: string; password: string }) => Promise<void>;\r\n  isLogin: boolean;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport const Form: FC<FormProps> = ({ handleSubmit, isLogin, isLoading }) => {\r\n  const [formData, setFormData] = useState({\r\n    email: \"\",\r\n    password: \"\",\r\n  });\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n\r\n  const onSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    await handleSubmit(formData);\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-6 w-full\">\r\n      <form onSubmit={onSubmit} className=\"flex flex-col gap-4 sm:gap-6 w-full\">\r\n        <div className=\"w-full\">\r\n          <InputField\r\n            label=\"Email\"\r\n            type=\"email\"\r\n            name=\"email\"\r\n            placeholder=\"Email\"\r\n            required\r\n            value={formData.email}\r\n            onChange={handleInputChange}\r\n            error={errors.email}\r\n          />\r\n        </div>\r\n        <div>\r\n          <PasswordInputField\r\n            label=\"Password\"\r\n            name=\"password\"\r\n            placeholder=\"Enter your password\"\r\n            required\r\n            value={formData.password}\r\n            onChange={handleInputChange}\r\n            error={errors.password}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\r\n          <div className=\"flex flex-col gap-2\">\r\n            <a\r\n              href=\"/auth/forgotPassword\"\r\n              className=\"text-sm text-blue-600 hover:text-blue-800\"\r\n            >\r\n              Forgot password?\r\n            </a>\r\n\r\n            <a\r\n              href=\"/auth/signup\"\r\n              className=\"text-sm text-blue-600 hover:text-blue-800\"\r\n            >\r\n              Don't have an account?\r\n            </a>\r\n          </div>\r\n          <Button\r\n            variant=\"primary\"\r\n            type=\"submit\"\r\n            icon={<FaArrowRight />}\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? \"Logging in...\" : \"Login\"}\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AADA;;;AALA;;;;;;AAoBO,MAAM,OAAsB,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE;;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAElD,MAAM,WAAW,OAAO;QACtB,EAAE,cAAc;QAChB,MAAM,aAAa;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAK,UAAU;YAAU,WAAU;;8BAClC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,sJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;wBACR,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,OAAO,OAAO,KAAK;;;;;;;;;;;8BAGvB,6LAAC;8BACC,cAAA,6LAAC,8JAAA,CAAA,qBAAkB;wBACjB,OAAM;wBACN,MAAK;wBACL,aAAY;wBACZ,QAAQ;wBACR,OAAO,SAAS,QAAQ;wBACxB,UAAU;wBACV,OAAO,OAAO,QAAQ;;;;;;;;;;;8BAI1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC,kJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,oBAAM,6LAAC,iJAAA,CAAA,eAAY;;;;;4BACnB,UAAU;sCAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GAxEa;KAAA"}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/login/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\n\r\nexport const LoginHeader: FC = () => {\r\n  return (\r\n    <h1 className=\"text-2xl sm:text-4xl font-medium mb-6\">\r\n      <span>Login to&nbsp;</span>\r\n      <span className=\"text-blue-500\">access</span>\r\n      <span>&nbsp;your profile</span>\r\n    </h1>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,cAAkB;IAC7B,qBACE,6LAAC;QAAG,WAAU;;0BACZ,6LAAC;0BAAK;;;;;;0BACN,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;0BAChC,6LAAC;0BAAK;;;;;;;;;;;;AAGZ;KARa"}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/login/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Layout } from \"../layout\";\r\nimport { Form } from \"./form\";\r\nimport { LoginHeader } from \"./header\";\r\n\r\ninterface LoginFormProps {\r\n  handleSubmit: (data: { email: string; password: string }) => Promise<void>;\r\n  isLoading: boolean;\r\n  isLogin: boolean;\r\n}\r\n\r\nexport const LoginForm: FC<LoginFormProps> = ({\r\n  handleSubmit,\r\n  isLoading,\r\n  isLogin,\r\n}) => {\r\n  const formContent = (\r\n    <Form handleSubmit={handleSubmit} isLoading={isLoading} isLogin={isLogin} />\r\n  );\r\n\r\n  return (\r\n    <Layout header={<LoginHeader />} formContent={formContent} isLogin={true} />\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;;;;;AAcO,MAAM,YAAgC,CAAC,EAC5C,YAAY,EACZ,SAAS,EACT,OAAO,EACR;IACC,MAAM,4BACJ,6LAAC,8IAAA,CAAA,OAAI;QAAC,cAAc;QAAc,WAAW;QAAW,SAAS;;;;;;IAGnE,qBACE,6LAAC,uIAAA,CAAA,SAAM;QAAC,sBAAQ,6LAAC,gJAAA,CAAA,cAAW;;;;;QAAK,aAAa;QAAa,SAAS;;;;;;AAExE;KAZa"}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/auth/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { LoginForm } from \"@components/auth/login\";\r\nimport { useAuth } from \"@hooks/useAuth\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { Suspense, useState } from \"react\";\r\n\r\nfunction LoginPageContent() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { login } = useAuth();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleSubmit = async (data: { email: string; password: string }) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const loginPromise = login(data.email, data.password);\r\n      const timeoutPromise = new Promise((_, reject) =>\r\n        setTimeout(() => reject(new Error(\"Login request timed out\")), 15000)\r\n      );\r\n\r\n      await Promise.race([loginPromise, timeoutPromise]);\r\n\r\n      const redirectParam = searchParams.get(\"redirect\");\r\n      if (redirectParam) {\r\n        router.replace(redirectParam);\r\n      } else {\r\n        const userInfoPromise = apiClient.get(\r\n          `/user/me?timezone=${\r\n            Intl.DateTimeFormat().resolvedOptions().timeZone\r\n          }`\r\n        );\r\n        const userInfoTimeoutPromise = new Promise((_, reject) =>\r\n          setTimeout(\r\n            () => reject(new Error(\"User info request timed out\")),\r\n            10000\r\n          )\r\n        );\r\n\r\n        const response = (await Promise.race([\r\n          userInfoPromise,\r\n          userInfoTimeoutPromise,\r\n        ])) as {\r\n          data: {\r\n            userType: string;\r\n            is_verified?: boolean;\r\n            isProfileComplete?: boolean;\r\n          };\r\n        };\r\n        const userInfo = response.data;\r\n\r\n        let redirectPath = \"\";\r\n\r\n        if (userInfo.userType === \"counselor\") {\r\n          redirectPath = userInfo.is_verified\r\n            ? \"/counselor/dashboard\"\r\n            : userInfo.isProfileComplete\r\n            ? \"/counselor/profile-complete\"\r\n            : \"/counselor/onboarding\";\r\n        } else {\r\n          // Students always go to dashboard regardless of profile completion\r\n          redirectPath = \"/student/dashboard\";\r\n        }\r\n\r\n        router.replace(redirectPath);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Login error:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <LoginForm\r\n      handleSubmit={handleSubmit}\r\n      isLoading={isLoading}\r\n      isLogin={true}\r\n    />\r\n  );\r\n}\r\n\r\nexport default function LoginPage() {\r\n  return (\r\n    <Suspense fallback={<></>}>\r\n      <LoginPageContent />\r\n    </Suspense>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,aAAa;QACb,IAAI;YACF,MAAM,eAAe,MAAM,KAAK,KAAK,EAAE,KAAK,QAAQ;YACpD,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,SACrC,WAAW,IAAM,OAAO,IAAI,MAAM,6BAA6B;YAGjE,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAc;aAAe;YAEjD,MAAM,gBAAgB,aAAa,GAAG,CAAC;YACvC,IAAI,eAAe;gBACjB,OAAO,OAAO,CAAC;YACjB,OAAO;gBACL,MAAM,kBAAkB,mHAAA,CAAA,UAAS,CAAC,GAAG,CACnC,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;gBAEJ,MAAM,yBAAyB,IAAI,QAAQ,CAAC,GAAG,SAC7C,WACE,IAAM,OAAO,IAAI,MAAM,iCACvB;gBAIJ,MAAM,WAAY,MAAM,QAAQ,IAAI,CAAC;oBACnC;oBACA;iBACD;gBAOD,MAAM,WAAW,SAAS,IAAI;gBAE9B,IAAI,eAAe;gBAEnB,IAAI,SAAS,QAAQ,KAAK,aAAa;oBACrC,eAAe,SAAS,WAAW,GAC/B,yBACA,SAAS,iBAAiB,GAC1B,gCACA;gBACN,OAAO;oBACL,mEAAmE;oBACnE,eAAe;gBACjB;gBAEA,OAAO,OAAO,CAAC;YACjB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,YAAS;QACR,cAAc;QACd,WAAW;QACX,SAAS;;;;;;AAGf;GAzES;;QACQ,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAClB,0HAAA,CAAA,UAAO;;;KAHlB;AA2EM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU;kBAClB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB"}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}