{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/BecomeCounselor.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { <PERSON>R<PERSON> } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"../../ui/button\";\r\nimport Lottie from \"lottie-react\";\r\nimport arrowAnimation from \"@/app/assets/lotties/Arrow.json\";\r\nimport webappAnimation from \"@/app/assets/lotties/WebappScreen.json\";\r\nimport starsAnimation from \"@/app/assets/lotties/Stars.json\";\r\n\r\nconst BecomeCounselor = () => {\r\n  return (\r\n    <div className=\"my-10 md:my-20 container mx-auto px-4 md:px-8 lg:px-16 xl:px-10 py-3 \">\r\n      {/* Main Content Grid */}\r\n      <div className=\"grid grid-cols-1 p-4 sm:p-6 md:p-8 lg:p-12 bg-gradient-to-br from-blue-950 to-black/90 border rounded-xl overflow-hidden relative\">\r\n        {/* Text Content */}\r\n        <div className=\"flex flex-col gap-y-4 sm:gap-y-6 md:gap-y-8 py-4 sm:py-8 lg:py-12 max-w-xl\">\r\n          <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-medium text-zinc-100 leading-tight\">\r\n            Share Your Knowledge. Make a Difference. Earn\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg md:text-xl text-zinc-100/90\">\r\n            Are you a current university student, alumnus, or certified expert?\r\n            Join our platform and guide aspiring students to success while\r\n            earning income.\r\n          </p>\r\n\r\n          <div className=\"flex gap-2 items-start relative pt-2 sm:pt-4\">\r\n            <Link href=\"/auth/signup\">\r\n              <Button className=\"bg-zinc-100 hover:bg-zinc-300 rounded-xl px-4 py-3 sm:p-6 text-blue-950 text-base sm:text-lg md:text-xl flex justify-center items-center\">\r\n                Join Now <ArrowRight className=\"h-5 w-5 sm:h-6 sm:w-6 ml-2\" />\r\n              </Button>\r\n            </Link>\r\n            {/* Arrow Lottie Animation */}\r\n            <div className=\"w-24 lg:w-36 h-24 lg:h-36 pt-6\">\r\n              <Lottie\r\n                animationData={arrowAnimation}\r\n                loop={true}\r\n                autoplay={true}\r\n                style={{ width: \"100%\", height: \"100%\" }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Stars Animation */}\r\n        <div className=\"hidden sm:block absolute right-[1rem] lg:right-2/4 top-4 w-16 sm:w-20 md:w-24 h-16 sm:h-20 md:h-24\">\r\n          <Lottie\r\n            animationData={starsAnimation}\r\n            loop={true}\r\n            autoplay={true}\r\n            style={{ width: \"100%\", height: \"100%\" }}\r\n          />\r\n        </div>\r\n\r\n        {/* Webapp Screen Animation - Only hidden on mobile */}\r\n        <div className=\"hidden lg:block absolute bottom-0 right-0 w-[45%] h-[90%] transform translate-x-[8%] translate-y-[5%]\">\r\n          <Lottie\r\n            animationData={webappAnimation}\r\n            loop={true}\r\n            autoplay={true}\r\n            style={{ width: \"100%\", height: \"100%\", objectFit: \"contain\" }}\r\n            className=\"rounded-t-xl\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BecomeCounselor;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AANA;AAGA;AALA;;;;;;;;;AAUA,MAAM,kBAAkB;IACtB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2E;;;;;;sCAGzF,8OAAC;4BAAE,WAAU;sCAAmD;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;4CAA2I;0DAClJ,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAInC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uKAAA,CAAA,UAAM;wCACL,eAAe,yGAAA,CAAA,UAAc;wCAC7B,MAAM;wCACN,UAAU;wCACV,OAAO;4CAAE,OAAO;4CAAQ,QAAQ;wCAAO;;;;;;;;;;;;;;;;;;;;;;;8BAM/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAA,CAAA,UAAM;wBACL,eAAe,yGAAA,CAAA,UAAc;wBAC7B,MAAM;wBACN,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;wBAAO;;;;;;;;;;;8BAK3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAA,CAAA,UAAM;wBACL,eAAe,gHAAA,CAAA,UAAe;wBAC9B,MAAM;wBACN,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;4BAAQ,WAAW;wBAAU;wBAC7D,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;uCAEe"}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/GetExpertHelp.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst GetExpertHelpSection = () => {\r\n    const features = [\r\n        {\r\n            icon: \"👨‍🏫\",\r\n            title: \"Expert Advisors Network\",\r\n            description:\r\n                \"Connect with verified advisors including current students and alumni from top universities, former admissions officers, and certified counselors.\",\r\n        },\r\n        {\r\n            icon: \"🎓\",\r\n            title: \"Proven Track Record\",\r\n            description:\r\n                \"Our students have achieved remarkable success, gaining admissions to Ivy League universities, UC schools, and other top-ranked institutions.\",\r\n        },\r\n        {\r\n            icon: \"🛡️\",\r\n            title: \"Secure & Guaranteed\",\r\n            description:\r\n                \"Experience peace of mind with secure transactions, comprehensive dispute resolution, and our satisfaction guarantee.\",\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <div className=\"w-full min-h-[400px] bg-gradient-to-br from-blue-950 to-black/90 text-white py-12 my-4 md:my-10\">\r\n            <div className=\"max-w-7xl mx-auto px-4\">\r\n                <div className=\"text-center mb-12\">\r\n                    <h4 className=\"text-sm uppercase tracking-wider mb-2\">\r\n                        ONE-ON-ONE ADVISING\r\n                    </h4>\r\n                    <h2 className=\"text-4xl font-medium mb-8\">\r\n                        Everything You Need for a Successful College Application\r\n                    </h2>\r\n                </div>\r\n\r\n                <div className=\"grid md:grid-cols-3 gap-6\">\r\n                    {features.map((feature, index) => (\r\n                        <div\r\n                            key={index}\r\n                            className=\"bg-white/10 backdrop-blur-lg rounded-lg p-6 hover:bg-white/15 transition-all\"\r\n                        >\r\n                            <div className=\"flex items-center mb-4\">\r\n                                <span className=\"text-2xl mr-3\">\r\n                                    {feature.icon}\r\n                                </span>\r\n                                <h3 className=\"text-xl font-medium\">\r\n                                    {feature.title}\r\n                                </h3>\r\n                            </div>\r\n                            <p className=\"text-gray-300 leading-relaxed\">\r\n                                {feature.description}\r\n                            </p>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default GetExpertHelpSection;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,uBAAuB;IACzB,MAAM,WAAW;QACb;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;QACA;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;QACA;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;KACH;IAED,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAG,WAAU;sCAA4B;;;;;;;;;;;;8BAK9C,8OAAC;oBAAI,WAAU;8BACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,8OAAC;4BAEG,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDACX,QAAQ,IAAI;;;;;;sDAEjB,8OAAC;4CAAG,WAAU;sDACT,QAAQ,KAAK;;;;;;;;;;;;8CAGtB,8OAAC;oCAAE,WAAU;8CACR,QAAQ,WAAW;;;;;;;2BAZnB;;;;;;;;;;;;;;;;;;;;;AAoBjC;uCAEe"}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/resources/card.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\nconst ResourceCard = ({ resource }: { resource: any }) => {\r\n  return (\r\n    <div\r\n      className=\"bg-white rounded-xl overflow-hidden cursor-pointer transition-all duration-200 border-2 hover:shadow-md p-2\"\r\n      onClick={() => window.open(resource.file_url)}\r\n    >\r\n      <div className=\"relative aspect-video\">\r\n        <Image\r\n          src={resource.image_url || 'https://placehold.co/600x400/e2e8f0/64748b?text=Resource'}\r\n          alt={resource.title}\r\n          fill\r\n          className=\"object-cover rounded-xl\"\r\n          unoptimized={!resource.image_url}\r\n        />\r\n      </div>\r\n      <div className=\"p-4\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className=\"text-sm font-medium text-gray-500\">\r\n            {resource.category}\r\n          </span>\r\n          <span className=\"text-sm text-gray-400\">\r\n            {new Date(resource.created_at).toLocaleDateString()}\r\n          </span>\r\n        </div>\r\n        <h3 className=\"font-semibold mb-2 line-clamp-2\">{resource.title}</h3>\r\n        <p className=\"text-sm text-gray-600 line-clamp-2\">\r\n          {resource.description}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResourceCard;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;IACnD,qBACE,8OAAC;QACC,WAAU;QACV,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS,QAAQ;;0BAE5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,SAAS,SAAS,IAAI;oBAC3B,KAAK,SAAS,KAAK;oBACnB,IAAI;oBACJ,WAAU;oBACV,aAAa,CAAC,SAAS,SAAS;;;;;;;;;;;0BAGpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,SAAS,QAAQ;;;;;;0CAEpB,8OAAC;gCAAK,WAAU;0CACb,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;kCAGrD,8OAAC;wBAAG,WAAU;kCAAmC,SAAS,KAAK;;;;;;kCAC/D,8OAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAK/B;uCAEe"}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Guides.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"../../ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport ResourceCard from \"../../student/resources/card\";\r\nimport { useResources } from \"@/app/hooks/student/useResources\";\r\n\r\nconst Guides = () => {\r\n  const { resources, setAudience } = useResources();\r\n\r\n  useEffect(() => {\r\n    setAudience(\"public\");\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"container mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10\">\r\n      <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center drop-shadow-lg px-3\">\r\n        <div className=\"self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900\">\r\n          Guides to help you grow\r\n        </div>\r\n        <div className=\"text-lg md:text-xl text-blue-950 px-2\">\r\n          Access a library of videos, templates, and examples curated by\r\n          Leland’s top coaches.\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 w-full h-max\">\r\n        {resources.slice(0, 4).map((resource) => (\r\n          <ResourceCard key={resource.id} resource={resource} />\r\n        ))}\r\n      </div>\r\n\r\n      <Link href=\"/library\">\r\n        <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center\">\r\n          View more <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n        </Button>\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Guides;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAFA;AALA;;;;;;;;AASA,MAAM,SAAS;IACb,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAE9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA0E;;;;;;kCAGzF,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC1B,8OAAC,kJAAA,CAAA,UAAY;wBAAmB,UAAU;uBAAvB,SAAS,EAAE;;;;;;;;;;0BAIlC,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,WAAU;;wBAAiH;sCACvH,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1C;uCAEe"}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Lot<PERSON> from \"lottie-react\";\r\nimport { ArrowRight, Search } from \"lucide-react\";\r\nimport animationData from \"@/app/assets/lotties/HomePageHeader.json\";\r\nimport { findClosestMatch } from \"@/app/utils/fuzzy-search\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport useClickOutside from \"@/app/hooks/student/useClickOutside\";\r\n\r\nconst SERVICES = [\r\n  \"Personal Statement Guidance\",\r\n  \"Essay Review\",\r\n  \"University Shortlisting\",\r\n  \"Extra Curricular Profile Building\",\r\n  \"Supplementary Essay Guidance\",\r\n  \"Financial Aid Advice\",\r\n  \"Interview Preparation\",\r\n];\r\n\r\nexport default function Hero() {\r\n  const router = useRouter();\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const [searchValue, setSearchValue] = useState(\"\");\r\n  const { setFilters } = useCounselors();\r\n\r\n  const searchContainerRef = useRef<HTMLDivElement>(null);\r\n  const searchInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Scroll search bar into view on mobile when suggestions open\r\n  useEffect(() => {\r\n    if (isExpanded && window.innerWidth < 768) {\r\n      const searchInput = searchInputRef.current;\r\n      if (searchInput) {\r\n        const rect = searchInput.getBoundingClientRect();\r\n        const scrollTop = window.scrollY || document.documentElement.scrollTop;\r\n        const viewportHeight = window.innerHeight;\r\n        // Position the search input 40% from the top of the screen\r\n        const targetPosition = rect.top + scrollTop - viewportHeight * 0.45;\r\n\r\n        window.scrollTo({\r\n          top: targetPosition,\r\n          behavior: \"smooth\",\r\n        });\r\n      }\r\n    }\r\n  }, [isExpanded]);\r\n\r\n  useClickOutside(searchContainerRef, () => {\r\n    setIsExpanded(false);\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!searchValue || !findClosestMatch(searchValue, SERVICES)) {\r\n      router.push(\"/explore\");\r\n      return;\r\n    }\r\n    const match = findClosestMatch(searchValue, SERVICES);\r\n    if (match) {\r\n      setSearchValue(match);\r\n      setFilters({ service_type: [match] });\r\n      router.push(\"/explore\");\r\n    }\r\n  };\r\n\r\n  const handleSuggestionClick = (service: string) => {\r\n    setSearchValue(service);\r\n    setFilters({ service_type: [service] });\r\n    router.push(\"/explore\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative w-full pb-10 overflow-hidden bg-white font-clash-display\">\r\n      {/* Lottie Background */}\r\n      <div className=\"hidden md:inline-block w-full transform md:-mt-24 2xl:-mt-0 z-10\">\r\n        <Lottie\r\n          animationData={animationData}\r\n          loop={true}\r\n          autoplay={true}\r\n          style={{ width: \"100%\", height: \"65rem\" }}\r\n        />\r\n      </div>\r\n\r\n      {/* Content Layer */}\r\n      <div className=\"relative md:absolute inset-0 mx-auto container pt-10 md:pt-[15vh] px-3 sm:px-6 lg:px-8 text-center\">\r\n        <h1 className=\"mb-6 font-[600] tracking-tight leading-[1.2] md:leading-[1.34] text-blue-950 text-[2.2rem] md:text-[4.5vw] lg:text-[4vw] xl:text-[3vw]\">\r\n          Find the Right Guidance for\r\n          <br className=\"my-4 md:inline-block hidden\" /> Your{\" \"}\r\n          <span className=\"text-[#800000] transition-all duration-500 ease-in-out hover:text-[#8B0000] hover:drop-shadow-sm cursor-default\">\r\n            College\r\n          </span>{\" \"}\r\n          Journey\r\n        </h1>\r\n        <p\r\n          className=\"mx-auto mb-12 max-w-2xl leading-[1.2] md:leading-[1.34] text-gray-600\"\r\n          style={{\r\n            fontSize: \"clamp(1rem, 2.5vw, 1.25rem)\",\r\n          }}\r\n        >\r\n          Search and connect with experienced mentors\r\n          <br className=\"my-4 md:inline-block hidden\" /> to get personalized\r\n          guidance for your university applications\r\n        </p>\r\n\r\n        {/* Search Form */}\r\n        <div\r\n          ref={searchContainerRef}\r\n          className=\"mx-auto w-[95%] md:w-[50vw] lg:w-[40vw] max-w-2xl relative\"\r\n        >\r\n          <form onSubmit={handleSubmit} className=\"relative\">\r\n            <div className=\"relative flex items-center\">\r\n              <Search className=\"absolute left-4 text-gray-500\" />\r\n              <input\r\n                ref={searchInputRef}\r\n                type=\"text\"\r\n                value={searchValue}\r\n                onChange={(e) => setSearchValue(e.target.value)}\r\n                onFocus={() => setIsExpanded(true)}\r\n                placeholder=\"What do you need help with today?\"\r\n                className=\"w-full rounded-lg bg-white py-4 md:py-8 pl-12 pr-12 text-[13px] md:text-base shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-transparent placeholder:text-gray-500 placeholder:text-[13px] md:placeholder:text-base\"\r\n              />\r\n              <button\r\n                type=\"submit\"\r\n                className=\"absolute right-2 rounded-lg bg-blue-950 p-2 text-white hover:bg-blue-950/90\"\r\n              >\r\n                <ArrowRight className=\"h-5 w-5\" />\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          {/* Suggestions Dropdown */}\r\n          <div\r\n            className={`\r\n                fixed md:absolute left-0 right-0 md:top-full bottom-0 md:bottom-auto md:mt-2\r\n                bg-white rounded-t-2xl md:rounded-lg shadow-2xl border border-gray-200 p-4\r\n                z-[9999] h-[60vh] md:h-auto md:max-h-[300px] overflow-y-auto transition-all duration-300 ease-out\r\n                ${\r\n                  isExpanded\r\n                    ? \"translate-y-0 opacity-100\"\r\n                    : \"translate-y-full md:translate-y-2 opacity-0 pointer-events-none\"\r\n                }\r\n              `}\r\n          >\r\n            <h3 className=\"text-sm font-medium text-gray-500 mb-3\">\r\n              Popular searches\r\n            </h3>\r\n            <div className=\"grid grid-cols-1 sm:flex sm:flex-wrap gap-2\">\r\n              {SERVICES.map((service) => (\r\n                <button\r\n                  key={service}\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    handleSuggestionClick(service);\r\n                    setIsExpanded(false);\r\n                  }}\r\n                  className=\"px-4 py-3 sm:px-3 sm:py-1.5 rounded-full bg-gray-100 text-[15px] sm:text-sm text-gray-700 hover:bg-gray-200 transition-colors cursor-pointer text-left sm:text-center\"\r\n                >\r\n                  {service}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AALA;AACA;AAAA;AANA;;;;;;;;;;AAYA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,UAAU,GAAG,KAAK;YACzC,MAAM,cAAc,eAAe,OAAO;YAC1C,IAAI,aAAa;gBACf,MAAM,OAAO,YAAY,qBAAqB;gBAC9C,MAAM,YAAY,OAAO,OAAO,IAAI,SAAS,eAAe,CAAC,SAAS;gBACtE,MAAM,iBAAiB,OAAO,WAAW;gBACzC,2DAA2D;gBAC3D,MAAM,iBAAiB,KAAK,GAAG,GAAG,YAAY,iBAAiB;gBAE/D,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF;QACF;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,0IAAA,CAAA,UAAe,AAAD,EAAE,oBAAoB;QAClC,cAAc;IAChB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,WAAW;YAC5D,OAAO,IAAI,CAAC;YACZ;QACF;QACA,MAAM,QAAQ,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;QAC5C,IAAI,OAAO;YACT,eAAe;YACf,WAAW;gBAAE,cAAc;oBAAC;iBAAM;YAAC;YACnC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,eAAe;QACf,WAAW;YAAE,cAAc;gBAAC;aAAQ;QAAC;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uKAAA,CAAA,UAAM;oBACL,eAAe,kHAAA,CAAA,UAAa;oBAC5B,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAyI;0CAErJ,8OAAC;gCAAG,WAAU;;;;;;4BAAgC;4BAAM;0CACpD,8OAAC;gCAAK,WAAU;0CAAkH;;;;;;4BAE1H;4BAAI;;;;;;;kCAGd,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,UAAU;wBACZ;;4BACD;0CAEC,8OAAC;gCAAG,WAAU;;;;;;4BAAgC;;;;;;;kCAKhD,8OAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,SAAS,IAAM,cAAc;4CAC7B,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC;gCACC,WAAW,CAAC;;;;gBAIR,EACE,aACI,8BACA,kEACL;cACH,CAAC;;kDAEH,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAEC,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,cAAc;gDAChB;gDACA,WAAU;0DAET;+CARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBvB"}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/LogoSlider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Slider from \"react-infinite-logo-slider\";\r\n\r\nconst logos_path = [\r\n  \"/images/institution-logos/1.png\",\r\n  \"/images/institution-logos/2.png\",\r\n  \"/images/institution-logos/3.svg\",\r\n  \"/images/institution-logos/4.png\",\r\n  \"/images/institution-logos/5.png\",\r\n  \"/images/institution-logos/6.png\",\r\n  \"/images/institution-logos/7.png\",\r\n  \"/images/institution-logos/8.png\",\r\n  \"/images/institution-logos/9.png\",\r\n  \"/images/institution-logos/10.png\",\r\n  \"/images/institution-logos/11.png\",\r\n];\r\n\r\nconst LogoSlider = () => {\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setLoaded(true);\r\n  }, []);\r\n  return (\r\n    <div className=\"container mx-auto flex flex-col justify-center items-start md:-mt-48 lg:-mt-32 xl:-mt-12 2xl:mt-10 mb-8 sm:mb-10 md:mb-16 sm:gap-y-8 gap-y-4 px-4 md:px-8 lg:px-16 xl:px-10 z-10\">\r\n      <div className=\"flex gap-1 md:gap-4 justify-start md:justify-center items-center\">\r\n        <h1 className=\"font-medium text-black text-2xl\">\r\n          Counselors from Top Universities:\r\n        </h1>\r\n        <Image\r\n          src={\"/svgs/red-arrow-diagonal.svg\"}\r\n          width={50}\r\n          height={50}\r\n          alt=\"\"\r\n          className=\"animate-pendulum origin-center\"\r\n        />\r\n      </div>\r\n      <div className=\"w-full overflow-hidden py-6 sm:py-10  bg-white rounded-xl shadow-transparent drop-shadow-[0_10px_30px_rgba(0,0,0,0.05)]\">\r\n        <Slider\r\n          width={loaded && window.innerWidth < 768 ? \"100px\" : \"160px\"}\r\n          duration={40}\r\n          pauseOnHover={false}\r\n          blurBorders={true}\r\n          blurBorderColor=\"#fff\"\r\n        >\r\n          {logos_path.map((logo, index) => (\r\n            <Slider.Slide key={index}>\r\n              <Image\r\n                width={800}\r\n                height={800}\r\n                src={logo}\r\n                alt={`Logo ${index + 1}`}\r\n                className=\"w-[3.3rem] md:w-20 object-cover h-auto\"\r\n              />\r\n            </Slider.Slide>\r\n          ))}\r\n        </Slider>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LogoSlider;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;IACZ,GAAG,EAAE;IACL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAGhD,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAI;wBACJ,WAAU;;;;;;;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,qKAAA,CAAA,UAAM;oBACL,OAAO,UAAU,OAAO,UAAU,GAAG,MAAM,UAAU;oBACrD,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,iBAAgB;8BAEf,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,qKAAA,CAAA,UAAM,CAAC,KAAK;sCACX,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,OAAO;gCACP,QAAQ;gCACR,KAAK;gCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;gCACxB,WAAU;;;;;;2BANK;;;;;;;;;;;;;;;;;;;;;AAc/B;uCAEe"}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/newsletter/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { zod<PERSON><PERSON>ol<PERSON> } from \"@hookform/resolvers/zod\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { useNewsletter } from \"@/app/hooks/public/useNewsletter\";\r\n\r\nconst newsletterSchema = z.object({\r\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\r\n  email: z.string().email(\"Please enter a valid email address\"),\r\n});\r\n\r\ntype NewsletterFormData = z.infer<typeof newsletterSchema>;\r\n\r\nexport function NewsletterForm() {\r\n  const { subscribe, loading } = useNewsletter();\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<NewsletterFormData>({\r\n    resolver: zodResolver(newsletterSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: NewsletterFormData) => {\r\n    try {\r\n      await subscribe(data);\r\n      reset();\r\n    } catch (error) {\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mx-auto max-w-7xl my-10 md:my-20  px-4 sm:px-6\">\r\n      <div className=\"bg-gradient-to-br from-blue-950 to-black/90 rounded-xl px-6 md:px-10 py-10 md:py-16 relative overflow-hidden\">\r\n        {/* Decorative dots pattern */}\r\n        {/* <div className=\"absolute top-0 left-0 grid grid-cols-7 gap-8 p-10\">\r\n        {[...Array(28)].map((_, i) => (\r\n          <div key={i} className=\"w-2 h-2 bg-blue-700/30 rounded-full\" />\r\n        ))}\r\n      </div> */}\r\n\r\n        {/* Content */}\r\n        <div className=\"relative z-10 max-w-4xl mx-auto\">\r\n          <h2 className=\"text-3xl md:text-4xl font-medium text-white mb-4\">\r\n            Subscribe to receive future updates\r\n          </h2>\r\n          <p className=\"text-blue-100 mb-8\">\r\n            If you want to get updates from us, please subscribe to our\r\n            newsletter.\r\n          </p>\r\n\r\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1\">\r\n                <Input\r\n                  {...register(\"name\")}\r\n                  placeholder=\"Enter your name\"\r\n                  className=\"w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500\"\r\n                />\r\n                {errors.name && (\r\n                  <p className=\"text-gray-300 text-sm mt-1\">\r\n                    {errors.name.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <Input\r\n                  {...register(\"email\")}\r\n                  type=\"email\"\r\n                  placeholder=\"Enter your email\"\r\n                  className=\"w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500\"\r\n                />\r\n                {errors.email && (\r\n                  <p className=\"text-gray-300 text-sm mt-1\">\r\n                    {errors.email.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"h-12 px-8 bg-blue-950 hover:bg-blue-950/90 text-gray-100 font-semibold transition-colors\"\r\n                disabled={loading}\r\n              >\r\n                {loading ? \"Subscribing...\" : \"Subscribe\"}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n\r\n          {/* Benefits section */}\r\n          {/* <div className=\"mt-12 flex flex-wrap gap-6 text-white text-sm\">\r\n            <span>$50 Raffle Entry</span>\r\n            <span>Essay Feedback from Ivy Experts</span>\r\n            <span>Claim Common App Extracurricular Entry</span>\r\n            <span>Q&A with Top College Admits</span>\r\n          </div> */}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NewsletterForm;\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AACA;AAHA;AADA;AAHA;;;;;;;;AASA,MAAM,mBAAmB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAIO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAC3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,UAAU;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBASb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAKlC,8OAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;kCAChD,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACH,GAAG,SAAS,OAAO;4CACpB,aAAY;4CACZ,WAAU;;;;;;wCAEX,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDACV,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAI1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACH,GAAG,SAAS,QAAQ;4CACrB,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;wCAEX,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDACV,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAI3B,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB9C;uCAEe"}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Testimonials.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\n\r\nconst TestimonialsSection = () => {\r\n  const testimonials = [\r\n    {\r\n      quote:\r\n        \"Before AdmitPath, I was overwhelmed with essays, deadlines, and choosing the right schools. My advisor helped me organize everything and gave me a strategy that worked. I got into my top-choice school!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Incoming College Freshman\",\r\n      highlight: \"I finally had a clear plan for my applications.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I thought my Common App essay was strong, but my AdmitPath advisor helped me refine it to truly reflect my story. The final version was much more compelling, and I got accepted into multiple Ivy League schools!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to Columbia University\",\r\n      highlight: \"The essay feedback completely changed my application!\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I was so nervous about my interviews, but my AdmitPath advisor gave me personalized feedback and mock interview practice. I went in prepared and confident—and got into my dream school!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to Stanford University\",\r\n      highlight:\r\n        \"I booked a session for interview prep, and it made all the difference.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I needed urgent help with my supplemental essays, and <PERSON>mit<PERSON><PERSON> connected me with an expert in just a few hours. Their feedback was incredibly detailed, and I know it helped strengthen my application!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to NYU\",\r\n      highlight: \"Fast, reliable, and actually helpful.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I didn't know how to showcase my extracurriculars to maximize merit-based scholarships. My advisor helped me position everything strategically, and I got into a top university with a full-ride scholarship!\",\r\n      author: \"Daniel R.\",\r\n      role: \"Admitted to USC with a Full-Ride Scholarship\",\r\n      highlight: \"I secured over $80,000 in scholarships thanks to my advisor.\",\r\n    },\r\n  ];\r\n\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [cardWidth, setCardWidth] = useState(33.333);\r\n  const [maxHeight, setMaxHeight] = useState(0);\r\n  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);\r\n\r\n  useEffect(() => {\r\n    const missionSection: HTMLElement | null =\r\n      document.querySelector(\".mission-section\");\r\n    const words: NodeListOf<HTMLElement> = document.querySelectorAll(\r\n      \".mission-section .word\"\r\n    );\r\n\r\n    const updateOpacity = (): void => {\r\n      if (!missionSection) return;\r\n\r\n      const sectionTop: number = missionSection.offsetTop;\r\n      const sectionHeight: number = missionSection.offsetHeight;\r\n      const scrollY: number = window.scrollY;\r\n      const windowHeight: number = window.innerHeight;\r\n\r\n      const sectionStart: number =\r\n        sectionTop - windowHeight + sectionHeight * 0.4;\r\n      const sectionEnd: number = sectionTop + sectionHeight;\r\n\r\n      if (scrollY >= sectionStart && scrollY <= sectionEnd) {\r\n        const scrollProgress: number =\r\n          (scrollY - sectionStart) / (sectionEnd - sectionStart);\r\n\r\n        words.forEach((word: HTMLElement, index: number) => {\r\n          const wordProgress: number =\r\n            scrollProgress - index * (0.5 / words.length);\r\n          const opacity: number = Math.min(\r\n            Math.max(wordProgress * words.length, 0.2),\r\n            1\r\n          );\r\n          word.style.opacity = opacity.toString();\r\n        });\r\n      } else if (scrollY < sectionStart) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"0.2\"));\r\n      } else if (scrollY > sectionEnd) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"1\"));\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", updateOpacity);\r\n    updateOpacity();\r\n\r\n    return () => window.removeEventListener(\"scroll\", updateOpacity);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (window.innerWidth < 640) {\r\n        setCardWidth(100);\r\n      } else if (window.innerWidth < 1024) {\r\n        setCardWidth(50);\r\n      } else {\r\n        setCardWidth(33.333);\r\n      }\r\n\r\n      // Reset height calculation\r\n      setTimeout(() => {\r\n        const heights = cardRefs.current\r\n          .filter((ref): ref is HTMLDivElement => ref !== null)\r\n          .map((ref) => ref.scrollHeight);\r\n        const maxCardHeight = Math.max(...heights);\r\n        setMaxHeight(maxCardHeight);\r\n      }, 100);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    handleResize();\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  const nextSlide = () => {\r\n    if (currentIndex < testimonials.length - 1) {\r\n      setCurrentIndex((prev) => prev + 1);\r\n    }\r\n  };\r\n\r\n  const prevSlide = () => {\r\n    if (currentIndex > 0) {\r\n      setCurrentIndex((prev) => prev - 1);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full bg-gray-50 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-24 py-12 sm:py-16\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"text-center mb-12\">\r\n          <div className=\"mission-section mb-8\">\r\n            <h2 className=\"mission_text text-2xl sm:text-3xl text-blue-950 font-bold\">\r\n              {\"Join thousands of students who found their path to success with AdmitPath\".split(\" \").map((word: string, index: number) => (\r\n                <span\r\n                  key={index}\r\n                  className=\"word inline-block opacity-20 leading-10 transition-opacity duration-300 ease-in-out\"\r\n                >\r\n                  {word}&nbsp;\r\n                </span>\r\n              ))}\r\n            </h2>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"relative\">\r\n          <div className=\"overflow-hidden\">\r\n            <div\r\n              className=\"flex transition-transform duration-500 ease-in-out py-2\"\r\n              style={{\r\n                transform: `translateX(-${currentIndex * cardWidth}%)`,\r\n              }}\r\n            >\r\n              {testimonials.map((testimonial, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-2 sm:px-3 md:px-4\"\r\n                >\r\n                  <div\r\n                    ref={(el) => {\r\n                      cardRefs.current[index] = el;\r\n                    }}\r\n                    className=\"bg-white rounded-lg p-6 transition-all duration-200 flex flex-col h-full border hover:shadow-md\"\r\n                    style={{ minHeight: maxHeight ? `${maxHeight}px` : \"auto\" }}\r\n                  >\r\n                    <div className=\"text-blue-950 font-medium text-base mb-3\">\r\n                      {testimonial.highlight}\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed flex-grow\">\r\n                      \"{testimonial.quote}\"\r\n                    </p>\r\n                    <div className=\"mt-4 pt-4 border-t\">\r\n                      <p className=\"font-semibold text-gray-900 text-sm\">\r\n                        {testimonial.author}\r\n                      </p>\r\n                      <p className=\"text-gray-500 text-xs\">\r\n                        {testimonial.role}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation Controls */}\r\n          <div className=\"mt-8 flex items-center justify-center gap-4 px-4\">\r\n            <button\r\n              onClick={prevSlide}\r\n              disabled={currentIndex === 0}\r\n              className=\"w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border\"\r\n              aria-label=\"Previous testimonial\"\r\n            >\r\n              <svg\r\n                className=\"w-4 h-4\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M15 19l-7-7 7-7\"\r\n                />\r\n              </svg>\r\n            </button>\r\n\r\n            {/* Progress Indicators */}\r\n            <div className=\"flex gap-1.5\">\r\n              {testimonials.map((_, idx) => (\r\n                <button\r\n                  key={idx}\r\n                  onClick={() => setCurrentIndex(idx)}\r\n                  className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentIndex ? 'bg-blue-950' : 'bg-gray-300 hover:bg-gray-400'}`}\r\n                  aria-label={`Go to testimonial ${idx + 1}`}\r\n                />\r\n              ))}\r\n            </div>\r\n\r\n            <button\r\n              onClick={nextSlide}\r\n              disabled={currentIndex === testimonials.length - 1}\r\n              className=\"w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border\"\r\n              aria-label=\"Next testimonial\"\r\n            >\r\n              <svg\r\n                className=\"w-4 h-4\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M9 5l7 7-7 7\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TestimonialsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,sBAAsB;IAC1B,MAAM,eAAe;QACnB;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WACE;QACJ;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA6B,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBACJ,SAAS,aAAa,CAAC;QACzB,MAAM,QAAiC,SAAS,gBAAgB,CAC9D;QAGF,MAAM,gBAAgB;YACpB,IAAI,CAAC,gBAAgB;YAErB,MAAM,aAAqB,eAAe,SAAS;YACnD,MAAM,gBAAwB,eAAe,YAAY;YACzD,MAAM,UAAkB,OAAO,OAAO;YACtC,MAAM,eAAuB,OAAO,WAAW;YAE/C,MAAM,eACJ,aAAa,eAAe,gBAAgB;YAC9C,MAAM,aAAqB,aAAa;YAExC,IAAI,WAAW,gBAAgB,WAAW,YAAY;gBACpD,MAAM,iBACJ,CAAC,UAAU,YAAY,IAAI,CAAC,aAAa,YAAY;gBAEvD,MAAM,OAAO,CAAC,CAAC,MAAmB;oBAChC,MAAM,eACJ,iBAAiB,QAAQ,CAAC,MAAM,MAAM,MAAM;oBAC9C,MAAM,UAAkB,KAAK,GAAG,CAC9B,KAAK,GAAG,CAAC,eAAe,MAAM,MAAM,EAAE,MACtC;oBAEF,KAAK,KAAK,CAAC,OAAO,GAAG,QAAQ,QAAQ;gBACvC;YACF,OAAO,IAAI,UAAU,cAAc;gBACjC,MAAM,OAAO,CAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;YAC7D,OAAO,IAAI,UAAU,YAAY;gBAC/B,MAAM,OAAO,CAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;YAC7D;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC;QAEA,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,UAAU,GAAG,KAAK;gBAC3B,aAAa;YACf,OAAO,IAAI,OAAO,UAAU,GAAG,MAAM;gBACnC,aAAa;YACf,OAAO;gBACL,aAAa;YACf;YAEA,2BAA2B;YAC3B,WAAW;gBACT,MAAM,UAAU,SAAS,OAAO,CAC7B,MAAM,CAAC,CAAC,MAA+B,QAAQ,MAC/C,GAAG,CAAC,CAAC,MAAQ,IAAI,YAAY;gBAChC,MAAM,gBAAgB,KAAK,GAAG,IAAI;gBAClC,aAAa;YACf,GAAG;QACL;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC;QACA,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI,eAAe,aAAa,MAAM,GAAG,GAAG;YAC1C,gBAAgB,CAAC,OAAS,OAAO;QACnC;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,eAAe,GAAG;YACpB,gBAAgB,CAAC,OAAS,OAAO;QACnC;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,4EAA4E,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAc,sBACzG,8OAAC;oCAEC,WAAU;;wCAET;wCAAK;;mCAHD;;;;;;;;;;;;;;;;;;;;8BAUf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,WAAW,CAAC,YAAY,EAAE,eAAe,UAAU,EAAE,CAAC;gCACxD;0CAEC,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CACC,KAAK,CAAC;gDACJ,SAAS,OAAO,CAAC,MAAM,GAAG;4CAC5B;4CACA,WAAU;4CACV,OAAO;gDAAE,WAAW,YAAY,GAAG,UAAU,EAAE,CAAC,GAAG;4CAAO;;8DAE1D,8OAAC;oDAAI,WAAU;8DACZ,YAAY,SAAS;;;;;;8DAExB,8OAAC;oDAAE,WAAU;;wDAAkD;wDAC3D,YAAY,KAAK;wDAAC;;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,YAAY,MAAM;;;;;;sEAErB,8OAAC;4DAAE,WAAU;sEACV,YAAY,IAAI;;;;;;;;;;;;;;;;;;uCArBlB;;;;;;;;;;;;;;;sCA+Bb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,iBAAiB;oCAC3B,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;8CAMR,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,oBACpB,8OAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,iDAAiD,EAAE,QAAQ,eAAe,gBAAgB,iCAAiC;4CACvI,cAAY,CAAC,kBAAkB,EAAE,MAAM,GAAG;2CAHrC;;;;;;;;;;8CAQX,8OAAC;oCACC,SAAS;oCACT,UAAU,iBAAiB,aAAa,MAAM,GAAG;oCACjD,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;uCAEe"}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/spinner.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface SpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n}\r\n\r\nexport const Spinner: React.FC<SpinnerProps> = ({ size = 'md', className = '' }) => {\r\n  const sizeClasses = {\r\n    sm: 'h-4 w-4',\r\n    md: 'h-8 w-8',\r\n    lg: 'h-12 w-12'\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex justify-center items-center\">\r\n      <div\r\n        className={`animate-spin rounded-full border-b-2 border-blue-950 ${sizeClasses[size]} ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"loading\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Spinner;\r\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAE;IAC7E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAC,qDAAqD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;YACnG,MAAK;YACL,cAAW;;;;;;;;;;;AAInB;uCAEe"}}, {"offset": {"line": 1447, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/TopCounselors.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useId, useState } from \"react\";\r\nimport * as CountryFlags from \"country-flag-icons/react/3x2\";\r\n\r\nconst getCountryFlag = (countryCode: string = \"US\") => {\r\n  const code = countryCode.toUpperCase();\r\n  const FlagComponent = (CountryFlags as any)[code];\r\n  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;\r\n};\r\nimport { Button } from \"../../ui/button\";\r\nimport { ArrowRight, GraduationCap } from \"lucide-react\";\r\n\r\nimport { Spinner } from \"@/app/components/ui/spinner\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport Slider from \"react-infinite-logo-slider\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport Image from \"next/image\";\r\n\r\nexport const Card = ({\r\n  profile,\r\n}: {\r\n  profile: Partial<CounselorPublicProfile>;\r\n}) => {\r\n  return (\r\n    <Link\r\n      href={`/profile/${profile.user_id}`}\r\n      className=\"block w-[280px] sm:w-[300px] md:w-[320px] min-h-[390px] sm:min-h-[410px] max-h-[390px] sm:max-h-[410px] mx-3 sm:mx-4 md:mx-5 my-4 sm:my-6 md:my-8 z-10 rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-500 cursor-pointer overflow-hidden hover:scale-[1.02] hover:-translate-y-1 hover:rotate-3 hover:ring-1 hover:ring-blue-950/30\"\r\n    >\r\n      <div className=\"relative h-[220px] w-full\">\r\n        <Image\r\n          width={300}\r\n          height={180}\r\n          className=\"w-full h-full object-cover\"\r\n          src={\r\n            profile.profile_picture_url ||\r\n            generatePlaceholder(profile.first_name, profile.last_name)\r\n          }\r\n          alt={`${profile.first_name}'s profile`}\r\n        />\r\n        <div className=\"absolute top-2 left-2 w-6 h-4 rounded-sm overflow-hidden shadow-md\">\r\n          {getCountryFlag(profile.country_of_residence)}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-4 flex flex-col h-[190px]\">\r\n        <div className=\"space-y-2 mb-2\">\r\n          <div className=\"flex flex-col gap-1.5\">\r\n            <div className=\"flex items-start justify-between gap-3\">\r\n              <h3 className=\"text-xl font-semibold text-gray-900 truncate\">\r\n                {profile.first_name}\r\n              </h3>\r\n              <p className=\"text-lg font-[500] text-blue-950 whitespace-nowrap flex-shrink-0\">\r\n                ${profile.hourly_rate}/hr\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"h-[36px] text-sm text-gray-600 mt-2\">\r\n            <p className=\"line-clamp-2\">\r\n              {profile?.tagline || profile?.bio || \"\"}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"h-[24px] mt-0.5\">\r\n            {profile.education && profile.education.length > 0 && (\r\n              <div className=\"flex items-center gap-2 text-sm\">\r\n                <GraduationCap className=\"w-4 h-4 text-blue-950 flex-shrink-0\" />\r\n                <p className=\"line-clamp-1 font-[500] text-[15px] text-blue-950\">\r\n                  {profile.education[0].university_name ||\r\n                    profile.education[0].major}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"border-t pt-1.5 mt-auto pb-1\">\r\n          <div className=\"relative overflow-hidden\">\r\n            <div className=\"absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-white via-white to-transparent z-10\"></div>\r\n            <div className=\"absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-white via-white to-transparent z-10\"></div>\r\n            <div className=\"flex whitespace-nowrap animate-[scroll_15s_linear_infinite] hover:pause-animation\">\r\n              <div className=\"flex gap-1.5 shrink-0\">\r\n                {[\r\n                  \"Essay Review\",\r\n                  \"Personal Statement\",\r\n                  \"College List Building\",\r\n                  \"Essay Brainstorming\",\r\n                ].map((service, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors\"\r\n                  >\r\n                    {service}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n              <div className=\"flex gap-1.5 shrink-0\">\r\n                {[\r\n                  \"Essay Review\",\r\n                  \"Personal Statement\",\r\n                  \"College List Building\",\r\n                  \"Essay Brainstorming\",\r\n                ].map((service, index) => (\r\n                  <span\r\n                    key={`duplicate-${index}`}\r\n                    className=\"inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors\"\r\n                  >\r\n                    {service}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nconst TopCounselors = () => {\r\n  const [topCounselors, setTopCounselors] = useState<CounselorPublicProfile[]>(\r\n    []\r\n  );\r\n  const { fetchTopCounselors, loading } = useCounselors();\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const loadTopCounselors = async () => {\r\n      try {\r\n        const counselors = await fetchTopCounselors();\r\n        setLoaded(true);\r\n        if (isMounted) {\r\n          setTopCounselors(counselors || []);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading counselors:\", error);\r\n        if (isMounted) {\r\n          setTopCounselors([]);\r\n        }\r\n      }\r\n    };\r\n\r\n    loadTopCounselors();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [fetchTopCounselors]);\r\n\r\n  return (\r\n    <div className=\"container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10\">\r\n      <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center px-3\">\r\n        <div className=\"self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900\">\r\n          Meet Our Top Counselors\r\n        </div>\r\n        <div className=\"text-lg md:text-xl text-blue-950 px-2\">\r\n          Explore our most trusted and highly rated mentors, ready to guide you\r\n          to success\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"w-full overflow-hidden\">\r\n        {loading && topCounselors.length === 0 ? (\r\n          <div className=\"flex justify-center items-center min-h-[300px]\">\r\n            <Spinner size=\"lg\" />\r\n          </div>\r\n        ) : (\r\n          <Slider\r\n            width={loaded && window.innerWidth < 640 ? \"330px\" : \"350px\"}\r\n            duration={60}\r\n            pauseOnHover={true}\r\n            blurBorders={false}\r\n          >\r\n            {[...topCounselors, ...topCounselors].map((counselor, index) => (\r\n              <Slider.Slide key={index}>\r\n                <Card profile={counselor} />\r\n              </Slider.Slide>\r\n            ))}\r\n          </Slider>\r\n        )}\r\n      </div>\r\n\r\n      <Link href=\"/explore\">\r\n        <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center\">\r\n          View More <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n        </Button>\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TopCounselors;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AAGA;AAEA;AACA;AACA;AACA;AAfA;AAQA;AAAA;AAZA;;;;;AAMA,MAAM,iBAAiB,CAAC,cAAsB,IAAI;IAChD,MAAM,OAAO,YAAY,WAAW;IACpC,MAAM,gBAAgB,AAAC,iKAAoB,CAAC,KAAK;IACjD,OAAO,8BAAgB,8OAAC;;;;6BAAmB,8OAAC,kKAAa,EAAE;;;;;AAC7D;;;;;;;;AAWO,MAAM,OAAO,CAAC,EACnB,OAAO,EAGR;IACC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;QACnC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,KACE,QAAQ,mBAAmB,IAC3B,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,UAAU,EAAE,QAAQ,SAAS;wBAE3D,KAAK,GAAG,QAAQ,UAAU,CAAC,UAAU,CAAC;;;;;;kCAExC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,QAAQ,oBAAoB;;;;;;;;;;;;0BAIhD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,QAAQ,UAAU;;;;;;sDAErB,8OAAC;4CAAE,WAAU;;gDAAmE;gDAC5E,QAAQ,WAAW;gDAAC;;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CACV,SAAS,WAAW,SAAS,OAAO;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,SAAS,CAAC,EAAE,CAAC,eAAe,IACnC,QAAQ,SAAS,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDAOX,8OAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oDAEC,WAAU;8DAET;mDAHI,CAAC,UAAU,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7C;AAEA,MAAM,gBAAgB;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,EAAE;IAEJ,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAEhB,MAAM,oBAAoB;YACxB,IAAI;gBACF,MAAM,aAAa,MAAM;gBACzB,UAAU;gBACV,IAAI,WAAW;oBACb,iBAAiB,cAAc,EAAE;gBACnC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,IAAI,WAAW;oBACb,iBAAiB,EAAE;gBACrB;YACF;QACF;QAEA;QAEA,OAAO;YACL,YAAY;QACd;IACF,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA0E;;;;;;kCAGzF,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;wBAAC,MAAK;;;;;;;;;;yCAGhB,8OAAC,qKAAA,CAAA,UAAM;oBACL,OAAO,UAAU,OAAO,UAAU,GAAG,MAAM,UAAU;oBACrD,UAAU;oBACV,cAAc;oBACd,aAAa;8BAEZ;2BAAI;2BAAkB;qBAAc,CAAC,GAAG,CAAC,CAAC,WAAW,sBACpD,8OAAC,qKAAA,CAAA,UAAM,CAAC,KAAK;sCACX,cAAA,8OAAC;gCAAK,SAAS;;;;;;2BADE;;;;;;;;;;;;;;;0BAQ3B,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,WAAU;;wBAAiH;sCACvH,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1C;uCAEe"}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/StickyFindCounselor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst StickyFindCounselor = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [lastScrollY, setLastScrollY] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const currentScrollY = window.scrollY;\r\n      \r\n      // Show when scrolling down and past 200px\r\n      if (currentScrollY > 200 && currentScrollY > lastScrollY) {\r\n        setIsVisible(true);\r\n      }\r\n      // Hide when scrolling up\r\n      else if (currentScrollY < lastScrollY) {\r\n        setIsVisible(false);\r\n      }\r\n      \r\n      setLastScrollY(currentScrollY);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [lastScrollY]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed bottom-6 right-6 z-50\">\r\n      <Link \r\n        href=\"/explore\"\r\n        className=\"\r\n          flex items-center gap-2\r\n          px-4 py-2\r\n          bg-[#800000]\r\n          hover:bg-[#600000]\r\n          text-white\r\n          rounded-lg\r\n          shadow-md\r\n          font-medium\r\n          text-sm\r\n        \"\r\n      >\r\n        <svg \r\n          xmlns=\"http://www.w3.org/2000/svg\" \r\n          fill=\"none\" \r\n          viewBox=\"0 0 24 24\" \r\n          strokeWidth={2} \r\n          stroke=\"currentColor\" \r\n          className=\"w-4 h-4\"\r\n        >\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\" />\r\n        </svg>\r\n        Find Counselor\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StickyFindCounselor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,sBAAsB;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YAErC,0CAA0C;YAC1C,IAAI,iBAAiB,OAAO,iBAAiB,aAAa;gBACxD,aAAa;YACf,OAEK,IAAI,iBAAiB,aAAa;gBACrC,aAAa;YACf;YAEA,eAAe;QACjB;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAY;IAEhB,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;;8BAYV,8OAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;gBACjD;;;;;;;;;;;;AAKd;uCAEe"}}, {"offset": {"line": 1925, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1931, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/FeaturedCategories.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card } from \"./TopCounselors\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  CounselorCategory,\r\n  useFeaturedCounselors,\r\n} from \"@/app/hooks/public/useFeaturedCounselors\";\r\n\r\nconst categories = [\r\n  {\r\n    id: \"top-ivy-league\" as Counselor<PERSON>ate<PERSON><PERSON>,\r\n    name: \"Top Ivy League\",\r\n    description: \"Expert counselors from Ivy League institutions\",\r\n  },\r\n  {\r\n    id: \"liberal-arts\" as CounselorCategory,\r\n    name: \"Liberal Arts\",\r\n    description: \"Specialists in Liberal Arts college admissions\",\r\n  },\r\n  {\r\n    id: \"top-ucs\" as CounselorCategor<PERSON>,\r\n    name: \"Top UCs\",\r\n    description: \"University of California system experts\",\r\n  },\r\n  {\r\n    id: \"russel-group\" as CounselorCategory,\r\n    name: \"Top UK\",\r\n    description: \"Leading counselors from UK's prestigious Russell Group\",\r\n  },\r\n];\r\n\r\nconst FeaturedCategories = () => {\r\n  const { counselors, isLoading, error, category, changeCategory } =\r\n    useFeaturedCounselors(\"top-ivy-league\");\r\n\r\n  return (\r\n    <div className=\"container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 px-4\">\r\n      <div className=\"w-full bg-gradient-to-br from-blue-950 to-black/90 rounded-2xl py-12 px-2 md:px-8 lg:px-16 xl:px-10 shadow-[0_4px_24px_-8px_rgba(0,0,0,0.1)]\">\r\n        <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center mb-8\">\r\n          <h2 className=\"text-2xl sm:text-4xl lg:text-5xl font-medium text-white\">\r\n            Featured Counselors\r\n          </h2>\r\n          <p className=\"text-lg md:text-xl text-blue-100 px-2\">\r\n            Find specialized counselors from top institutions worldwide\r\n          </p>\r\n        </div>\r\n\r\n        {/* Category Tabs */}\r\n        <div className=\"w-full flex justify-center  mb-6\">\r\n          <div className=\"flex flex-wrap justify-center items-center gap-2 md:gap-4 max-w-4xl\">\r\n            {categories.map((cat) => (\r\n              <button\r\n                key={cat.id}\r\n                onClick={() => changeCategory(cat.id)}\r\n                className={cn(\r\n                  \"px-4 py-2 rounded-full text-sm md:text-base font-medium transition-all duration-200\",\r\n                  category === cat.id\r\n                    ? \"bg-white text-blue-950\"\r\n                    : \"bg-white/10 text-white hover:bg-white/20\"\r\n                )}\r\n              >\r\n                {cat.name}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Description */}\r\n        <div className=\"w-full flex justify-center px-4\">\r\n          <p className=\"text-blue-100 text-center max-w-2xl mb-8\">\r\n            {categories.find((c) => c.id === category)?.description}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Loading State */}\r\n        {isLoading && (\r\n          <div className=\"w-full flex justify-center py-12\">\r\n            <div className=\"animate-pulse text-blue-100\">\r\n              Loading counselors...\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error State */}\r\n        {error && (\r\n          <div className=\"w-full flex justify-center py-8\">\r\n            <div className=\"text-red-400 text-center\">{error}</div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Counselors Grid */}\r\n        {!isLoading && !error && (\r\n          <div className=\"flex flex-wrap justify-center items-center gap-6 w-full\">\r\n            {counselors.length > 0 ? (\r\n              counselors.map((counselor) => (\r\n                <Card key={counselor.user_id} profile={counselor} />\r\n              ))\r\n            ) : (\r\n              <div className=\"w-full text-center text-blue-100 py-8\">\r\n                No counselors found in this category.\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeaturedCategories;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AASA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;CACD;AAED,MAAM,qBAAqB;IACzB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAC9D,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE;IAExB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;sCAGxE,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAMvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uFACA,aAAa,IAAI,EAAE,GACf,2BACA;0CAGL,IAAI,IAAI;+BATJ,IAAI,EAAE;;;;;;;;;;;;;;;8BAgBnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,WAAW,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,WAAW;;;;;;;;;;;gBAK/C,2BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAA8B;;;;;;;;;;;gBAOhD,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;;;;;;gBAK9C,CAAC,aAAa,CAAC,uBACd,8OAAC;oBAAI,WAAU;8BACZ,WAAW,MAAM,GAAG,IACnB,WAAW,GAAG,CAAC,CAAC,0BACd,8OAAC,wJAAA,CAAA,OAAI;4BAAyB,SAAS;2BAA5B,UAAU,OAAO;;;;kDAG9B,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;;;;;AASrE;uCAEe"}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}