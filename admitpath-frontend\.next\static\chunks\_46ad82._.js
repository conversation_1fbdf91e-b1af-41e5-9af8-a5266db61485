(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_46ad82._.js", {

"[project]/lib/apiClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use client";
;
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000") || "http://localhost:8000",
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json"
    }
});
// Request interceptor to handle dynamic headers or logging
apiClient.interceptors.request.use((config)=>{
    const token = localStorage.getItem("access_token");
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor to handle common errors globally
apiClient.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // Handle specific error responses (e.g., 401 Unauthorized)
    if (error.response) {
        const { status } = error.response;
        if (status === 401) {
            if (window.location.pathname.startsWith("/student") || window.location.pathname.startsWith("/counselor") || localStorage.getItem("access_token")) {
                console.error("Unauthorized. Redirecting to login...");
                localStorage.removeItem("access_token");
                window.location.href = "/auth/login";
            }
        } else if (status >= 500) {
            console.error("Server error:", error.response.data.message || "Internal Server Error");
        }
    } else {
        console.error("Network error:", error.message);
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/counselor/useProfile.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "useProfile": (()=>useProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
"use client";
;
;
const useProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        loading: false,
        error: null,
        userInfo: null,
        personalInfo: null,
        educationInfo: null,
        experienceInfo: null,
        documentInfo: null,
        profilePicture: null,
        getUserInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`);
                set({
                    userInfo: response.data
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        getPersonalInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profile/personal-info");
                set({
                    personalInfo: response.data
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        getEducationInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profile/education");
                set({
                    educationInfo: response.data
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        getExperienceInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profile/professional-experience");
                set({
                    experienceInfo: response.data
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        getDocumentInfo: async ()=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("/counselor/profile/documents");
                set({
                    documentInfo: response.data
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            } finally{
                set({
                    loading: false
                });
            }
        },
        // Upload Profile Picture
        uploadProfilePicture: async (e)=>{
            const file = e.target.files ? e.target.files[0] : null;
            const validFiles = [
                "image/png",
                "image/jpeg",
                "image/jpg",
                "image/webp"
            ];
            if (!file) {
                alert("No image selected.");
                return;
            }
            if (!validFiles.includes(file.type)) {
                alert("Invalid file type. Please upload an image in png, jpeg, jpg, or webp format.");
                return;
            }
            const formData = new FormData();
            formData.append("file", file);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("/user/profile-picture", formData, {
                    headers: {
                        "Content-Type": "multipart/form-data"
                    }
                });
                set({
                    profilePicture: response.data.profile_picture_url
                });
            } catch (error) {
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            }
        },
        deleteProfilePicture: async ()=>{
            try {
                // The correct endpoint should be the same as the backend's configured route
                await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete("/user/profile-picture");
                // After deletion, refetch user info to update the UI
                const { getUserInfo } = get();
                await getUserInfo();
                set({
                    profilePicture: null
                });
            } catch (error) {
                console.error("Error deleting profile picture:", error);
                set({
                    error: error.response?.data?.detail || "Something went wrong"
                });
                throw error;
            }
        },
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/counselor/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>CounselorPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$counselor$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/hooks/counselor/useProfile.ts [app-client] (ecmascript)");
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
function CounselorPage({ children }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { userInfo, getUserInfo } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$counselor$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProfile"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CounselorPage.useEffect": ()=>{
            if (!localStorage.getItem("access_token")) {
                router.replace("/auth/login");
                return;
            }
            getUserInfo();
        }
    }["CounselorPage.useEffect"], [
        getUserInfo
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CounselorPage.useEffect": ()=>{
            if (userInfo) {
                const { isProfileComplete, is_verified } = userInfo;
                // If not a counselor, redirect to student dashboard
                if (userInfo.userType !== "counselor") {
                    router.replace("/student/dashboard");
                    return;
                }
                const isOnboardingPath = pathname?.startsWith("/counselor/onboarding");
                const isDashboardPath = pathname?.startsWith("/counselor/dashboard");
                // If profile is not complete, only allow access to onboarding
                if (!isProfileComplete && !isOnboardingPath) {
                    router.replace("/counselor/onboarding");
                    return;
                }
                // If profile is complete but not verified, show completion page
                if (isProfileComplete && !is_verified && (isDashboardPath || isOnboardingPath)) {
                    router.replace("/counselor/profile-complete");
                    return;
                }
                // If profile is complete and verified, don't allow access to onboarding
                if (isProfileComplete && is_verified && isOnboardingPath) {
                    router.replace("/counselor/dashboard");
                    return;
                }
            }
        }
    }["CounselorPage.useEffect"], [
        userInfo,
        router,
        pathname
    ]);
    return children;
}
_s(CounselorPage, "IZlQVA6bfhXSbRlHJVG96j1iV74=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$counselor$2f$useProfile$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProfile"]
    ];
});
_c = CounselorPage;
var _c;
__turbopack_refresh__.register(_c, "CounselorPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/counselor/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=_46ad82._.js.map