{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/passwordInputField/index.module.css"], "sourcesContent": [".passwordContainer {\r\n  position: relative;\r\n}\r\n\r\n.inputIcon {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 2.3rem;\r\n  z-index: 10;\r\n  cursor: pointer;\r\n  border-style: none;\r\n  background-color: transparent;\r\n  padding: 0.25rem;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\r\n}\r\n\r\n.eyeIcon {\r\n  height: 1.25rem;\r\n  width: 1.25rem;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\r\n}\r\n\r\n.eyeIcon:hover {\r\n  --tw-text-opacity: 1;\r\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\r\n}\r\n\r\n.passwordTooltip {\r\n  position: absolute;\r\n  top: 2.5rem;\r\n  right: 0px;\r\n  z-index: 10;\r\n  margin-top: 0.5rem;\r\n  width: 250px;\r\n  border-radius: 0.5rem;\r\n  border-width: 1px;\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\r\n  padding: 1rem;\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n\r\n.passwordTooltip ul {\r\n  list-style-type: none;\r\n}\r\n\r\n.passwordTooltip ul > :not([hidden]) ~ :not([hidden]) {\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\r\n}\r\n\r\n.passwordTooltip li::before {\r\n  content: '✔';\r\n  margin-right: 0.5rem;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;AAIA;;;;;;AAMA"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}