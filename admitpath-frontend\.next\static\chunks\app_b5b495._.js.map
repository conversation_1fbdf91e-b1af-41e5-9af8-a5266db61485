{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/popup/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactNode } from \"react\";\r\n\r\ninterface PopupProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title?: string;\r\n  width?: string;\r\n  height?: string;\r\n  children: ReactNode;\r\n}\r\n\r\nconst Popup: React.FC<PopupProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  width,\r\n  height = \"80vh\",\r\n  children,\r\n}) => {\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div\r\n        className=\"bg-white rounded-xl shadow-lg relative overflow-hidden w-[95vw] md:w-[80vw] lg:w-[60vw] max-w-4xl\"\r\n        style={{ width }}\r\n      >\r\n        <div className=\"bg-grayLight p-4 mb-4\">\r\n          {title && <h2 className=\"text-xl font-semibold \">{title}</h2>}\r\n          <button\r\n            onClick={onClose}\r\n            className=\"absolute top-5 right-4 text-xs text-gray-500 hover:text-black\"\r\n            aria-label=\"Close\"\r\n          >\r\n            ✖\r\n          </button>\r\n        </div>\r\n        <div\r\n          className=\"popupz p-4 pb-6 overflow-y-auto overflow-x-hidden\"\r\n          style={{ maxHeight: height }}\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Popup;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,SAAS,MAAM,EACf,QAAQ,EACT;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE;YAAM;;8BAEf,6LAAC;oBAAI,WAAU;;wBACZ,uBAAS,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAClD,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCACZ;;;;;;;;;;;;8BAIH,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW;oBAAO;8BAE1B;;;;;;;;;;;;;;;;;AAKX;KAnCM;uCAqCS"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/mainBtn/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconProp } from \"@fortawesome/fontawesome-svg-core\";\r\n\r\ninterface ButtonProps {\r\n  type?: \"submit\" | \"reset\" | \"button\";\r\n  children: React.ReactNode;\r\n  onClick?: (\r\n    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>\r\n  ) => void;\r\n  style?: React.CSSProperties;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"neutral\";\r\n  icon?: IconProp;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const MainButton: React.FC<ButtonProps> = ({\r\n  type,\r\n  children,\r\n  onClick,\r\n  style,\r\n  className = \"\",\r\n  variant = \"neutral\",\r\n  icon,\r\n  disabled = false,\r\n}) => {\r\n  // Default styles for button variants\r\n  const variantStyles = {\r\n    primary: \"bg-mainClr text-white hover:bg-[#152070] focus:ring-blue-300\",\r\n    secondary: \"bg-[#EB5757] text-white hover:bg-[#EB6767]\",\r\n    neutral: \"bg-neutral1 text-neutral10 hover:bg-neutral2 focus:ring-neutral3\",\r\n  };\r\n\r\n  const disabledStyles = \"opacity-50 cursor-not-allowed\";\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={`py-3 px-6 font-medium rounded-xl text-sm flex items-center justify-center gap-2 \r\n        ${variantStyles[variant]} ${\r\n        disabled ? disabledStyles : \"\"\r\n      } ${className}`}\r\n      style={style}\r\n      onClick={!disabled ? onClick : undefined}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {icon && <FontAwesomeIcon icon={icon} />}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAmBO,MAAM,aAAoC,CAAC,EAChD,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,IAAI,EACJ,WAAW,KAAK,EACjB;IACC,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAC;QACV,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAC1B,WAAW,iBAAiB,GAC7B,CAAC,EAAE,WAAW;QACf,OAAO;QACP,SAAS,CAAC,WAAW,UAAU;QAC/B,UAAU;;YAET;YACA,sBAAQ,6LAAC,uKAAA,CAAA,kBAAe;gBAAC,MAAM;;;;;;;;;;;;AAGtC;KAlCa"}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/popup/PendingPayments.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faArrowUp,\r\n  faArrowDown,\r\n  faArrowRight,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport Image from \"next/image\";\r\nimport { MainButton } from \"../../../common/mainBtn\";\r\n\r\ninterface Payment {\r\n  id: number;\r\n  studentName: string;\r\n  sessionStatus: string;\r\n  serviceName: string;\r\n  amount: string;\r\n  action: string;\r\n}\r\n\r\nconst payments: Payment[] = [\r\n  {\r\n    id: 1,\r\n    studentName: \"John Doe\",\r\n    sessionStatus: \"Completed\",\r\n    serviceName: \"Math Tutoring\",\r\n    amount: \"$200\",\r\n    action: \"Request Payment\",\r\n  },\r\n  {\r\n    id: 2,\r\n    studentName: \"<PERSON>\",\r\n    sessionStatus: \"Pending\",\r\n    serviceName: \"Science Tutoring\",\r\n    amount: \"$150\",\r\n    action: \"Request Pending\",\r\n  },\r\n  {\r\n    id: 3,\r\n    studentName: \"<PERSON>\",\r\n    sessionStatus: \"Completed\",\r\n    serviceName: \"English Tutoring\",\r\n    amount: \"$300\",\r\n    action: \"Payment Received\",\r\n  },\r\n  {\r\n    id: 4,\r\n    studentName: \"<PERSON> Brown\",\r\n    sessionStatus: \"Cancelled\",\r\n    serviceName: \"History Tutoring\",\r\n    amount: \"$100\",\r\n    action: \"View Details\",\r\n  },\r\n];\r\n\r\nconst statusClasses: Record<string, string> = {\r\n  Completed: \"bg-green-300\",\r\n  Pending: \"bg-yellow-300\",\r\n  Cancelled: \"bg-red-400\",\r\n  default: \"bg-gray-400\",\r\n};\r\n\r\nconst actionClasses: Record<string, string> = {\r\n  \"Request Payment\": \"bg-blue-300\",\r\n  \"View Details\": \"bg-green-300\",\r\n  \"Payment Received\": \"bg-black\",\r\n  \"Request Pending\": \"bg-yellow-400\",\r\n};\r\n\r\ntype PaymentKeys = keyof Payment;\r\n\r\nexport default function PendingPayments({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n}: PopupProps) {\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: PaymentKeys;\r\n    direction: \"asc\" | \"desc\";\r\n  }>({ key: \"studentName\", direction: \"asc\" });\r\n\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [selectAll, setSelectAll] = useState(false);\r\n\r\n  const sortedPayments = [...payments].sort((a, b) => {\r\n    if (a[sortConfig.key] < b[sortConfig.key]) {\r\n      return sortConfig.direction === \"asc\" ? -1 : 1;\r\n    }\r\n    if (a[sortConfig.key] > b[sortConfig.key]) {\r\n      return sortConfig.direction === \"asc\" ? 1 : -1;\r\n    }\r\n    return 0;\r\n  });\r\n\r\n  function handleSort(newKey: PaymentKeys) {\r\n    setSortConfig((prevConfig) => ({\r\n      key: newKey,\r\n      direction:\r\n        prevConfig.key === newKey && prevConfig.direction === \"asc\"\r\n          ? \"desc\"\r\n          : \"asc\",\r\n    }));\r\n  }\r\n\r\n  const getClass = (item: string, classes: Record<string, string>) =>\r\n    classes[item] || classes.default;\r\n\r\n  const renderSortIcon = (key: PaymentKeys) => (\r\n    <FontAwesomeIcon\r\n      icon={\r\n        sortConfig.key === key && sortConfig.direction === \"asc\"\r\n          ? faArrowUp\r\n          : faArrowDown\r\n      }\r\n      className=\"ml-2 text-blue-500\"\r\n    />\r\n  );\r\n\r\n  // Handle individual row selection\r\n  const handleRowSelect = (id: number) => {\r\n    setSelectedRows((prev) =>\r\n      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]\r\n    );\r\n  };\r\n\r\n  // Handle select all toggle\r\n  const handleSelectAll = () => {\r\n    if (selectAll) {\r\n      setSelectedRows([]); // Deselect all\r\n    } else {\r\n      setSelectedRows(payments.map((payment) => payment.id)); // Select all\r\n    }\r\n    setSelectAll(!selectAll);\r\n  };\r\n\r\n  // Update select all checkbox state when individual checkboxes change\r\n  const isChecked = (id: number) => selectedRows.includes(id);\r\n\r\n  return (\r\n    <div className=\"p-4\">\r\n      <Popup\r\n        isOpen={isPopupOpen}\r\n        onClose={() => setIsPopupOpen(false)}\r\n        title=\"Pending Payments ($2400)\"\r\n        width=\"70vw\"\r\n      >\r\n        <div className=\"content p-3\">\r\n          <div className=\"\">\r\n            <table className=\"table-auto w-full border-collapse\">\r\n              <thead className=\"rounded-xl border overflow-hidden\">\r\n                <tr className=\"border-b bg-gray-100 text-sm font-semibold\">\r\n                  <th className=\"px-4 py-2 text-left\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={selectAll}\r\n                      onChange={handleSelectAll}\r\n                    />\r\n                  </th>\r\n                  {[\r\n                    { label: \"Student Name\", key: \"studentName\" },\r\n                    { label: \"Session Status\", key: \"sessionStatus\" },\r\n                    { label: \"Service Name\", key: \"serviceName\" },\r\n                    { label: \"Amount\", key: \"amount\" },\r\n                  ].map(({ label, key }) => (\r\n                    <th\r\n                      key={key}\r\n                      className=\"px-4 py-2 cursor-pointer\"\r\n                      onClick={() => handleSort(key as PaymentKeys)}\r\n                    >\r\n                      <span className=\"mr-2\">{label}</span>\r\n                      {renderSortIcon(key as PaymentKeys)}\r\n                    </th>\r\n                  ))}\r\n                  <th className=\"px-4 py-2\">Action</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {sortedPayments.map((payment) => (\r\n                  <tr key={payment.id} className=\"border-b text-sm\">\r\n                    <td className=\"px-4 py-2\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={isChecked(payment.id)}\r\n                        onChange={() => handleRowSelect(payment.id)}\r\n                      />\r\n                    </td>\r\n                    <td className=\"px-4 py-2 flex items-center gap-3\">\r\n                      <Image\r\n                        src=\"/images/avatar.png\"\r\n                        alt=\"User Pic\"\r\n                        width={40}\r\n                        height={40}\r\n                      />\r\n                      <div className=\"text-sm\">\r\n                        <span className=\"block mb-1\">\r\n                          {payment.studentName}\r\n                        </span>\r\n                        <span className=\"block\"><EMAIL></span>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-4 py-2\">\r\n                      <button\r\n                        className={`px-4 py-1 rounded-lg text-white text-sm whitespace-nowrap ${getClass(\r\n                          payment.sessionStatus,\r\n                          statusClasses\r\n                        )}`}\r\n                      >\r\n                        {payment.sessionStatus}\r\n                      </button>\r\n                    </td>\r\n                    <td className=\"px-4 py-2\">{payment.serviceName}</td>\r\n                    <td className=\"px-4 py-2\">{payment.amount}</td>\r\n                    <td className=\"px-4 py-2\">\r\n                      <button\r\n                        className={`px-4 py-1 rounded-lg text-white text-sm whitespace-nowrap ${getClass(\r\n                          payment.action,\r\n                          actionClasses\r\n                        )}`}\r\n                      >\r\n                        {payment.action}\r\n                      </button>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          <div className=\"flex justify-end gap-3 ml-auto mt-6\">\r\n            <MainButton\r\n              variant=\"neutral\"\r\n              children=\"Cancel\"\r\n              onClick={() => setIsPopupOpen(false)}\r\n            />\r\n            <MainButton\r\n              variant=\"primary\"\r\n              type=\"submit\"\r\n              children=\"Request all\"\r\n              icon={faArrowRight}\r\n            />\r\n          </div>\r\n        </div>\r\n      </Popup>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AAEA;AACA;AARA;;;AAJA;;;;;;;AAuBA,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,gBAAwC;IAC5C,WAAW;IACX,SAAS;IACT,WAAW;IACX,SAAS;AACX;AAEA,MAAM,gBAAwC;IAC5C,mBAAmB;IACnB,gBAAgB;IAChB,oBAAoB;IACpB,mBAAmB;AACrB;AAIe,SAAS,gBAAgB,EACtC,WAAW,EACX,cAAc,EACH;;IACX,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxC;QAAE,KAAK;QAAe,WAAW;IAAM;IAE1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB;WAAI;KAAS,CAAC,IAAI,CAAC,CAAC,GAAG;QAC5C,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE;YACzC,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;QAC/C;QACA,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,EAAE;YACzC,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;QAC/C;QACA,OAAO;IACT;IAEA,SAAS,WAAW,MAAmB;QACrC,cAAc,CAAC,aAAe,CAAC;gBAC7B,KAAK;gBACL,WACE,WAAW,GAAG,KAAK,UAAU,WAAW,SAAS,KAAK,QAClD,SACA;YACR,CAAC;IACH;IAEA,MAAM,WAAW,CAAC,MAAc,UAC9B,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO;IAElC,MAAM,iBAAiB,CAAC,oBACtB,6LAAC,uKAAA,CAAA,kBAAe;YACd,MACE,WAAW,GAAG,KAAK,OAAO,WAAW,SAAS,KAAK,QAC/C,2KAAA,CAAA,YAAS,GACT,2KAAA,CAAA,cAAW;YAEjB,WAAU;;;;;;IAId,kCAAkC;IAClC,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,CAAC,OACf,KAAK,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,QAAU,UAAU,MAAM;mBAAI;gBAAM;aAAG;IAE5E;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,IAAI,WAAW;YACb,gBAAgB,EAAE,GAAG,eAAe;QACtC,OAAO;YACL,gBAAgB,SAAS,GAAG,CAAC,CAAC,UAAY,QAAQ,EAAE,IAAI,aAAa;QACvE;QACA,aAAa,CAAC;IAChB;IAEA,qEAAqE;IACrE,MAAM,YAAY,CAAC,KAAe,aAAa,QAAQ,CAAC;IAExD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iJAAA,CAAA,UAAK;YACJ,QAAQ;YACR,SAAS,IAAM,eAAe;YAC9B,OAAM;YACN,OAAM;sBAEN,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;;;;;;;;;;;4CAGb;gDACC;oDAAE,OAAO;oDAAgB,KAAK;gDAAc;gDAC5C;oDAAE,OAAO;oDAAkB,KAAK;gDAAgB;gDAChD;oDAAE,OAAO;oDAAgB,KAAK;gDAAc;gDAC5C;oDAAE,OAAO;oDAAU,KAAK;gDAAS;6CAClC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,iBACnB,6LAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,WAAW;;sEAE1B,6LAAC;4DAAK,WAAU;sEAAQ;;;;;;wDACvB,eAAe;;mDALX;;;;;0DAQT,6LAAC;gDAAG,WAAU;0DAAY;;;;;;;;;;;;;;;;;8CAG9B,6LAAC;8CACE,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,MAAK;wDACL,SAAS,UAAU,QAAQ,EAAE;wDAC7B,UAAU,IAAM,gBAAgB,QAAQ,EAAE;;;;;;;;;;;8DAG9C,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,QAAQ,WAAW;;;;;;8EAEtB,6LAAC;oEAAK,WAAU;8EAAQ;;;;;;;;;;;;;;;;;;8DAG5B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,WAAW,CAAC,0DAA0D,EAAE,SACtE,QAAQ,aAAa,EACrB,gBACC;kEAEF,QAAQ,aAAa;;;;;;;;;;;8DAG1B,6LAAC;oDAAG,WAAU;8DAAa,QAAQ,WAAW;;;;;;8DAC9C,6LAAC;oDAAG,WAAU;8DAAa,QAAQ,MAAM;;;;;;8DACzC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,WAAW,CAAC,0DAA0D,EAAE,SACtE,QAAQ,MAAM,EACd,gBACC;kEAEF,QAAQ,MAAM;;;;;;;;;;;;2CAzCZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCAkD3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mJAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,UAAS;gCACT,SAAS,IAAM,eAAe;;;;;;0CAEhC,6LAAC,mJAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,MAAK;gCACL,UAAS;gCACT,MAAM,2KAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;GA7KwB;KAAA"}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/popup/StudentsFeedback.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport { FaStar } from \"react-icons/fa\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { Feedback } from \"@/app/types/counselor/sessions\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { format } from \"date-fns\";\r\n\r\ninterface FeedbackProps extends PopupProps {\r\n  feedbackList: Feedback | null;\r\n}\r\n\r\nexport default function StudentsFeedback({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n  feedbackList,\r\n}: FeedbackProps) {\r\n  const [selectedButton, setSelectedButton] = useState<string>(\"All\");\r\n\r\n  const buttons = [\"All\", \"5\", \"4+\", \"3+\", \"2+\", \"1+\"];\r\n\r\n  // Filter feedback based on selected button\r\n  const filteredFeedback =\r\n    selectedButton === \"All\"\r\n      ? feedbackList?.reviews\r\n      : feedbackList?.reviews.filter((feedback) =>\r\n          selectedButton.includes(\"+\")\r\n            ? feedback.rating >= parseInt(selectedButton)\r\n            : feedback.rating === parseInt(selectedButton)\r\n        );\r\n\r\n  return (\r\n    <div className=\"p-4\">\r\n      <Popup\r\n        isOpen={isPopupOpen}\r\n        onClose={() => setIsPopupOpen(false)}\r\n        title={`Students Feedback (${feedbackList?.total_reviews} reviews)`}\r\n      >\r\n        <div className=\"content p-4\">\r\n          <div className=\"btn-groups flex gap-5\">\r\n            {buttons.map((button) => (\r\n              <button\r\n                key={button}\r\n                onClick={() => setSelectedButton(button)}\r\n                className={`py-2 px-5 rounded-3xl border flex items-center gap-2 ${\r\n                  selectedButton === button ? \"bg-mainClr text-white\" : \"\"\r\n                }`}\r\n              >\r\n                {button === \"All\" ? (\r\n                  button\r\n                ) : (\r\n                  <>\r\n                    <FaStar className=\"text-yellow-500\" /> {button}\r\n                  </>\r\n                )}\r\n              </button>\r\n            ))}\r\n          </div>\r\n\r\n          <ul className=\"feedback-container mt-7 grid gap-3\">\r\n            {filteredFeedback && filteredFeedback.length > 0 ? (\r\n              filteredFeedback.map((feedback, index) => (\r\n                <li\r\n                  key={feedback.id}\r\n                  className=\"bg-white border rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer p-4 flex\"\r\n                >\r\n                  {/* Student Avatar */}\r\n                  <div className=\"mr-4 flex-shrink-0\">\r\n                    <img\r\n                      src={\r\n                        feedback.student_profile_picture ||\r\n                        generatePlaceholder(\r\n                          feedback.student_name.split(/s+/)[0],\r\n                          feedback.student_name.split(/s+/)[1]\r\n                        )\r\n                      }\r\n                      alt={feedback.student_name}\r\n                      className=\"w-12 h-12 rounded-full object-cover\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Review Content */}\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"font-medium mb-1\">\r\n                      {feedback.student_name}\r\n                    </div>\r\n                    {/* Star Rating */}\r\n                    <div className=\"flex mb-2\">\r\n                      {[1, 2, 3, 4, 5].map((star) => (\r\n                        <svg\r\n                          key={star}\r\n                          className={`w-4 h-4 ${\r\n                            star <= feedback.rating\r\n                              ? \"text-yellow-400\"\r\n                              : \"text-gray-300\"\r\n                          }`}\r\n                          fill=\"currentColor\"\r\n                          viewBox=\"0 0 20 20\"\r\n                        >\r\n                          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n                        </svg>\r\n                      ))}\r\n                    </div>\r\n\r\n                    {/* Review Comment */}\r\n                    <p className=\"text-gray-700 mb-2 line-clamp-3\">\r\n                      {feedback.comment}\r\n                    </p>\r\n\r\n                    {/* Review Date */}\r\n                    <p className=\"text-gray-500 text-sm\">\r\n                      {format(new Date(feedback.created_at), \"PPP\")}\r\n                    </p>\r\n                  </div>\r\n                </li>\r\n              ))\r\n            ) : (\r\n              <p className=\"text-center text-gray-500\">\r\n                No feedback available for this filter.\r\n              </p>\r\n            )}\r\n          </ul>\r\n\r\n          <div className=\"mt-6 flex justify-end\">\r\n            <MainButton\r\n              variant=\"neutral\"\r\n              children=\"Close\"\r\n              onClick={() => setIsPopupOpen(false)}\r\n            />\r\n          </div>\r\n        </div>\r\n      </Popup>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AAEA;AAHA;AAIA;;;AATA;;;;;;;AAee,SAAS,iBAAiB,EACvC,WAAW,EACX,cAAc,EACd,YAAY,EACE;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,UAAU;QAAC;QAAO;QAAK;QAAM;QAAM;QAAM;KAAK;IAEpD,2CAA2C;IAC3C,MAAM,mBACJ,mBAAmB,QACf,cAAc,UACd,cAAc,QAAQ,OAAO,CAAC,WAC5B,eAAe,QAAQ,CAAC,OACpB,SAAS,MAAM,IAAI,SAAS,kBAC5B,SAAS,MAAM,KAAK,SAAS;IAGzC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iJAAA,CAAA,UAAK;YACJ,QAAQ;YACR,SAAS,IAAM,eAAe;YAC9B,OAAO,CAAC,mBAAmB,EAAE,cAAc,cAAc,SAAS,CAAC;sBAEnE,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,qDAAqD,EAC/D,mBAAmB,SAAS,0BAA0B,IACtD;0CAED,WAAW,QACV,uBAEA;;sDACE,6LAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAoB;wCAAE;;;+BAVvC;;;;;;;;;;kCAiBX,6LAAC;wBAAG,WAAU;kCACX,oBAAoB,iBAAiB,MAAM,GAAG,IAC7C,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC9B,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KACE,SAAS,uBAAuB,IAChC,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,SAAS,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EACpC,SAAS,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;4CAGxC,KAAK,SAAS,YAAY;4CAC1B,WAAU;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,SAAS,YAAY;;;;;;0DAGxB,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAG;oDAAG;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;wDAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,SAAS,MAAM,GACnB,oBACA,iBACJ;wDACF,MAAK;wDACL,SAAQ;kEAER,cAAA,6LAAC;4DAAK,GAAE;;;;;;uDATH;;;;;;;;;;0DAeX,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO;;;;;;0DAInB,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,UAAU,GAAG;;;;;;;;;;;;;+BAhDtC,SAAS,EAAE;;;;sDAsDpB,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;kCAM7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,UAAS;4BACT,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA1HwB;KAAA"}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/popup/EventDetailsModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function EventDetailsModal({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n}: PopupProps) {\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Event Details\"\r\n      width=\"50vw\"\r\n    >\r\n      <p className=\"text-[neutral8] text-md font-medium mb-2\">\r\n        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ad, placeat?\r\n      </p>\r\n      <p className=\"text-[neutral8] text-sm font-medium mb-4\">\r\n        <span>icon</span> Thursday, December 12:15 - 2:00pm\r\n      </p>\r\n      <button className=\"text-sm bg-[#6FCF9733] py-2 px-4 rounded-lg mb-4\">\r\n        Tag here\r\n      </button>\r\n\r\n      <hr />\r\n\r\n      <p className=\"mt-8 mb-4 text-sm text-neutral8 font-medium\">Guest</p>\r\n      <div className=\"flex justify-between items-center text-sm text-neutral8 font-medium mb-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <Image\r\n            src=\"/images/avatar.png\"\r\n            alt=\"user pic\"\r\n            height={60}\r\n            width={60}\r\n          />\r\n          <h3>Student Name</h3>\r\n        </div>\r\n        <MainButton variant=\"primary\">Send Message</MainButton>\r\n      </div>\r\n      <p className=\"text-sm text-neutral8 font-medium mb-2\">Meeting Link</p>\r\n      <div className=\"flex gap-3 items-center mb-6\">\r\n        <Image\r\n          src=\"/images/avatar.png\"\r\n          alt=\"Zoom link\"\r\n          height={40}\r\n          width={40}\r\n        />\r\n        <Link href=\"/#\">\r\n          Lorem ipsum dolor sit amet consectetur adipisicing elit. Quia,\r\n          aspernatur?\r\n        </Link>\r\n      </div>\r\n\r\n      <hr />\r\n\r\n      <div className=\"btn-groups flex justify-between items-center mt-6\">\r\n        <button className=\"text-[#EB5757] px-6\">Remove</button>\r\n\r\n        <div className=\"flex gap-3\">\r\n          <MainButton variant=\"neutral\">Edit</MainButton>\r\n          <MainButton variant=\"neutral\" onClick={() => setIsPopupOpen(false)}>\r\n            Close\r\n          </MainButton>\r\n        </div>\r\n      </div>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQe,SAAS,kBAAkB,EACxC,WAAW,EACX,cAAc,EACH;IACX,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;QACN,OAAM;;0BAEN,6LAAC;gBAAE,WAAU;0BAA2C;;;;;;0BAGxD,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;kCAAK;;;;;;oBAAW;;;;;;;0BAEnB,6LAAC;gBAAO,WAAU;0BAAmD;;;;;;0BAIrE,6LAAC;;;;;0BAED,6LAAC;gBAAE,WAAU;0BAA8C;;;;;;0BAC3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,QAAQ;gCACR,OAAO;;;;;;0CAET,6LAAC;0CAAG;;;;;;;;;;;;kCAEN,6LAAC,mJAAA,CAAA,aAAU;wBAAC,SAAQ;kCAAU;;;;;;;;;;;;0BAEhC,6LAAC;gBAAE,WAAU;0BAAyC;;;;;;0BACtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,QAAQ;wBACR,OAAO;;;;;;kCAET,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAK;;;;;;;;;;;;0BAMlB,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;kCAAsB;;;;;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mJAAA,CAAA,aAAU;gCAAC,SAAQ;0CAAU;;;;;;0CAC9B,6LAAC,mJAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAO9E;KAhEwB"}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/popup/EditEventModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Popup from \"@/app/components/common/popup\";\r\nimport { PopupProps } from \"@/app/types/counselor/profile\";\r\n\r\nexport default function EditEventModal({\r\n  isPopupOpen,\r\n  setIsPopupOpen,\r\n}: PopupProps) {\r\n  return (\r\n    <Popup\r\n      isOpen={isPopupOpen}\r\n      onClose={() => setIsPopupOpen(false)}\r\n      title=\"Edit Event\"\r\n      width=\"50vw\"\r\n    >\r\n      <h2>EDit evnet</h2>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS,eAAe,EACrC,WAAW,EACX,cAAc,EACH;IACX,qBACE,6LAAC,iJAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,eAAe;QAC9B,OAAM;QACN,OAAM;kBAEN,cAAA,6LAAC;sBAAG;;;;;;;;;;;AAGV;KAdwB"}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/loadingSpinner/index.tsx"], "sourcesContent": ["export default function LoadingSpinner() {\r\n  return (\r\n    <div role=\"status\" className=\"absolute left-1/2 -translate-x-1/2 pt-4\">\r\n      <svg\r\n        aria-hidden=\"true\"\r\n        className=\"w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\r\n        viewBox=\"0 0 100 101\"\r\n        fill=\"none\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n      >\r\n        <path\r\n          d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\r\n          fill=\"currentFill\"\r\n        />\r\n      </svg>\r\n      <span className=\"sr-only\">Loading...</span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,MAAK;QAAS,WAAU;;0BAC3B,6LAAC;gBACC,eAAY;gBACZ,WAAU;gBACV,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;KAtBwB"}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/SessionCard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport LoadingSpinner from \"../../common/loadingSpinner\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\ninterface Session {\r\n  id: number;\r\n  event_name: string;\r\n  date: string;\r\n  start_time: string;\r\n  end_time: string;\r\n  meeting_link: string;\r\n  student_name: string;\r\n}\r\n\r\nconst SessionCards: React.FC = () => {\r\n  const [sessions, setSessions] = useState<Session[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchSessions = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await apiClient.get(\r\n          `/counselor/dashboard/upcoming-sessions?timezone=${\r\n            Intl.DateTimeFormat().resolvedOptions().timeZone\r\n          }&limit=3`\r\n        );\r\n        setSessions(response.data.sessions || []);\r\n        setError(null);\r\n      } catch (err) {\r\n        setError(\"Failed to fetch data\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchSessions();\r\n  }, []);\r\n\r\n  const latestSessions = [...sessions].sort(\r\n    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()\r\n  );\r\n\r\n  return (\r\n    <section>\r\n      {loading ? (\r\n        <div className=\"text-center text-neutral-500\">\r\n          <LoadingSpinner />\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"text-center text-red-500\">\r\n          <p>{error}</p>\r\n        </div>\r\n      ) : latestSessions.length > 0 ? (\r\n        latestSessions.map((session) => (\r\n          <div\r\n            key={session.id}\r\n            className=\"border rounded-xl p-4 bg-white hover:shadow-xl transition-shadow mt-4\"\r\n          >\r\n            <h3 className=\"font-semibold text-lg mb-2\">{session.event_name}</h3>\r\n            <p className=\"text-sm text-gray-600\">\r\n              <span className=\"font-bold\">Date:</span>{\" \"}\r\n              {new Date(session.date).toLocaleDateString()}\r\n            </p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              <span className=\"font-bold\">Start Time:</span>{\" \"}\r\n              {new Date(session.start_time).toLocaleTimeString()} (GMT+05:00)\r\n            </p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              <span className=\"font-bold\">End Time:</span>{\" \"}\r\n              {new Date(session.end_time).toLocaleTimeString()} (GMT+05:00)\r\n            </p>\r\n            <p className=\"text-sm text-gray-600\">\r\n              <span className=\"font-bold\">Student:</span> {session.student_name}\r\n            </p>\r\n            <a\r\n              href={session.meeting_link}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-blue-500 underline hover:text-blue-700 text-sm mt-2 inline-block\"\r\n            >\r\n              Join Meeting\r\n            </a>\r\n          </div>\r\n        ))\r\n      ) : (\r\n        <div className=\"text-center text-neutral-500 mt-10\">\r\n          <p>No Sessions Available</p>\r\n        </div>\r\n      )}\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default SessionCards;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAYA,MAAM,eAAyB;;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB;oBACpB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,gDAAgD,EAC/C,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,CACjD,QAAQ,CAAC;wBAEZ,YAAY,SAAS,IAAI,CAAC,QAAQ,IAAI,EAAE;wBACxC,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,iBAAiB;WAAI;KAAS,CAAC,IAAI,CACvC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAGjE,qBACE,6LAAC;kBACE,wBACC,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,0JAAA,CAAA,UAAc;;;;;;;;;mBAEf,sBACF,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;0BAAG;;;;;;;;;;mBAEJ,eAAe,MAAM,GAAG,IAC1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCAA8B,QAAQ,UAAU;;;;;;kCAC9D,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAY;;;;;;4BAAa;4BACxC,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB;;;;;;;kCAE5C,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAY;;;;;;4BAAmB;4BAC9C,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;4BAAG;;;;;;;kCAErD,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAY;;;;;;4BAAiB;4BAC5C,IAAI,KAAK,QAAQ,QAAQ,EAAE,kBAAkB;4BAAG;;;;;;;kCAEnD,6LAAC;wBAAE,WAAU;;0CACX,6LAAC;gCAAK,WAAU;0CAAY;;;;;;4BAAe;4BAAE,QAAQ,YAAY;;;;;;;kCAEnE,6LAAC;wBACC,MAAM,QAAQ,YAAY;wBAC1B,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;;eAxBI,QAAQ,EAAE;;;;sCA8BnB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;0BAAE;;;;;;;;;;;;;;;;AAKb;GA/EM;KAAA;uCAiFS"}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/EarningsSummary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Bar } from \"react-chartjs-2\";\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n} from \"chart.js\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport LoadingSpinner from \"@/app/components/common/loadingSpinner\";\r\n\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  BarElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend\r\n);\r\n\r\ntype MonthlyData = {\r\n  month: string;\r\n  total: number;\r\n  approved: number;\r\n  pending: number;\r\n};\r\n\r\ntype EarningsData = {\r\n  total_earning: number;\r\n  approved: number;\r\n  pending: number;\r\n  monthly_data: MonthlyData[];\r\n};\r\n\r\nconst EarningsSummary: React.FC = () => {\r\n  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [windowWidth, setWindowWidth] = useState<number>(\r\n    typeof window !== \"undefined\" ? window.innerWidth : 1200\r\n  );\r\n\r\n  useEffect(() => {\r\n    const fetchEarningsData = async () => {\r\n      try {\r\n        const response = await apiClient.get(\r\n          \"/counselor/dashboard/earnings-summary\"\r\n        );\r\n\r\n        const data: EarningsData = response.data;\r\n        setEarningsData(data);\r\n      } catch (err) {\r\n        setError(\"Earning data is empty\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchEarningsData();\r\n\r\n    // Handle window resize for responsiveness\r\n    const handleResize = () => {\r\n      setWindowWidth(window.innerWidth);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Render loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"p-3 sm:p-6 bg-gray-100\">\r\n        <div className=\"bg-white shadow-md rounded-lg p-4 text-center\">\r\n          <LoadingSpinner />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"p-3 sm:p-6 bg-gray-100\">\r\n        <div className=\"bg-white shadow-md rounded-lg p-4 text-center\">\r\n          <p className=\"text-red-500\">{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render no data state\r\n  if (!earningsData) {\r\n    return (\r\n      <div className=\"p-3 sm:p-6 bg-gray-100\">\r\n        <div className=\"bg-white shadow-md rounded-lg p-4 text-center\">\r\n          <p className=\"text-gray-500\">No data available.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Format currency\r\n  const formatCurrency = (amount: number) => {\r\n    return new Intl.NumberFormat(\"en-US\", {\r\n      style: \"currency\",\r\n      currency: \"USD\",\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0,\r\n    }).format(amount);\r\n  };\r\n\r\n  // On mobile, abbreviate month names\r\n  const getAdjustedLabels = () => {\r\n    return earningsData.monthly_data.map((data) => {\r\n      if (windowWidth < 640) {\r\n        // Abbreviate month names on small screens\r\n        return data.month.substring(0, 3);\r\n      }\r\n      return data.month;\r\n    });\r\n  };\r\n\r\n  const data = {\r\n    labels: getAdjustedLabels(),\r\n    datasets: [\r\n      {\r\n        label: \"Total Earnings\",\r\n        data: earningsData.monthly_data.map((data) => data.total),\r\n        backgroundColor: \"#4CAF50\", // Green for total\r\n      },\r\n      {\r\n        label: \"Approved\",\r\n        data: earningsData.monthly_data.map((data) => data.approved),\r\n        backgroundColor: \"#2196F3\", // Blue for approved\r\n      },\r\n      {\r\n        label: \"Pending\",\r\n        data: earningsData.monthly_data.map((data) => data.pending),\r\n        backgroundColor: \"#FF9800\", // Orange for pending\r\n      },\r\n    ],\r\n  };\r\n\r\n  const options = {\r\n    responsive: true,\r\n    maintainAspectRatio: false,\r\n    plugins: {\r\n      legend: {\r\n        position: windowWidth < 640 ? (\"bottom\" as const) : (\"top\" as const),\r\n        labels: {\r\n          boxWidth: windowWidth < 640 ? 10 : 20,\r\n          padding: windowWidth < 640 ? 10 : 20,\r\n          font: {\r\n            size: windowWidth < 640 ? 10 : 12,\r\n          },\r\n        },\r\n      },\r\n      title: {\r\n        display: true,\r\n        text: \"Monthly Earnings Overview\",\r\n        font: {\r\n          size: windowWidth < 640 ? 14 : 16,\r\n        },\r\n      },\r\n      tooltip: {\r\n        callbacks: {\r\n          label: function (context: any) {\r\n            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;\r\n          },\r\n        },\r\n      },\r\n    },\r\n    scales: {\r\n      x: {\r\n        ticks: {\r\n          font: {\r\n            size: windowWidth < 640 ? 10 : 12,\r\n          },\r\n        },\r\n      },\r\n      y: {\r\n        ticks: {\r\n          font: {\r\n            size: windowWidth < 640 ? 10 : 12,\r\n          },\r\n          callback: function (value: any) {\r\n            return formatCurrency(value);\r\n          },\r\n        },\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-0 sm:p-6 bg-gray-100\">\r\n      <div className=\"bg-white shadow-md border rounded-lg p-3 sm:p-4\">\r\n        {/* Summary Cards for Quick Overview */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 mb-4\">\r\n          <div className=\"bg-green-50 p-3 rounded-lg border border-green-100 shadow-sm\">\r\n            <p className=\"text-xs sm:text-sm text-green-600 font-medium\">\r\n              Total Earnings\r\n            </p>\r\n            <p className=\"text-lg sm:text-xl font-bold text-green-600\">\r\n              {formatCurrency(earningsData.total_earning)}\r\n            </p>\r\n          </div>\r\n          <div className=\"bg-blue-50 p-3 rounded-lg border border-blue-100 shadow-sm\">\r\n            <p className=\"text-xs sm:text-sm text-blue-600 font-medium\">\r\n              Approved\r\n            </p>\r\n            <p className=\"text-lg sm:text-xl font-bold text-blue-600\">\r\n              {formatCurrency(earningsData.approved)}\r\n            </p>\r\n          </div>\r\n          <div className=\"bg-orange-50 p-3 rounded-lg border border-orange-100 shadow-sm\">\r\n            <p className=\"text-xs sm:text-sm text-orange-600 font-medium\">\r\n              Pending\r\n            </p>\r\n            <p className=\"text-lg sm:text-xl font-bold text-orange-600\">\r\n              {formatCurrency(earningsData.pending)}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Chart Container with Fixed Height for Better Mobile Display */}\r\n        <div className=\"h-64 sm:h-80 md:h-96\">\r\n          <Bar data={data} options={options} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EarningsSummary;\r\n"], "names": [], "mappings": ";;;;AAEA;AAWA;AACA;AAVA;AADA;;;AAHA;;;;;;AAgBA,+JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,+JAAA,CAAA,gBAAa,EACb,+JAAA,CAAA,cAAW,EACX,+JAAA,CAAA,aAAU,EACV,+JAAA,CAAA,QAAK,EACL,+JAAA,CAAA,UAAO,EACP,+JAAA,CAAA,SAAM;AAiBR,MAAM,kBAA4B;;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3C,uCAAgC,OAAO,UAAU;IAGnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;+DAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC;wBAGF,MAAM,OAAqB,SAAS,IAAI;wBACxC,gBAAgB;oBAClB,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,0CAA0C;YAC1C,MAAM;0DAAe;oBACnB,eAAe,OAAO,UAAU;gBAClC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,uBAAuB;IACvB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0JAAA,CAAA,UAAc;;;;;;;;;;;;;;;IAIvB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,uBAAuB;IACvB,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,oCAAoC;IACpC,MAAM,oBAAoB;QACxB,OAAO,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI,cAAc,KAAK;gBACrB,0CAA0C;gBAC1C,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG;YACjC;YACA,OAAO,KAAK,KAAK;QACnB;IACF;IAEA,MAAM,OAAO;QACX,QAAQ;QACR,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,KAAK;gBACxD,iBAAiB;YACnB;YACA;gBACE,OAAO;gBACP,MAAM,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;gBAC3D,iBAAiB;YACnB;YACA;gBACE,OAAO;gBACP,MAAM,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;gBAC1D,iBAAiB;YACnB;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,UAAU,cAAc,MAAO,WAAsB;gBACrD,QAAQ;oBACN,UAAU,cAAc,MAAM,KAAK;oBACnC,SAAS,cAAc,MAAM,KAAK;oBAClC,MAAM;wBACJ,MAAM,cAAc,MAAM,KAAK;oBACjC;gBACF;YACF;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,MAAM;oBACJ,MAAM,cAAc,MAAM,KAAK;gBACjC;YACF;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAU,OAAY;wBAC3B,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,eAAe,QAAQ,GAAG,GAAG;oBACnE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,OAAO;oBACL,MAAM;wBACJ,MAAM,cAAc,MAAM,KAAK;oBACjC;gBACF;YACF;YACA,GAAG;gBACD,OAAO;oBACL,MAAM;wBACJ,MAAM,cAAc,MAAM,KAAK;oBACjC;oBACA,UAAU,SAAU,KAAU;wBAC5B,OAAO,eAAe;oBACxB;gBACF;YACF;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCAAE,WAAU;8CACV,eAAe,aAAa,aAAa;;;;;;;;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,6LAAC;oCAAE,WAAU;8CACV,eAAe,aAAa,QAAQ;;;;;;;;;;;;sCAGzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAiD;;;;;;8CAG9D,6LAAC;oCAAE,WAAU;8CACV,eAAe,aAAa,OAAO;;;;;;;;;;;;;;;;;;8BAM1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yJAAA,CAAA,MAAG;wBAAC,MAAM;wBAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;AAKpC;GAtMM;KAAA;uCAwMS"}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/counselor/useSessions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  NotesPayload,\r\n  SessionsState,\r\n  Session,\r\n} from \"@/app/types/counselor/sessions\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nexport const useSessions = create<SessionsState>((set, get) => ({\r\n  loading: false,\r\n  error: null,\r\n  sessions: null,\r\n  currentSession: null,\r\n  currentNotes: null,\r\n  studentsFeedback: null,\r\n\r\n  fetchSessions: async (timezone, params) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(`/counselor/counseling-sessions?timezone=${timezone}`, {\r\n        params,\r\n      });\r\n      set({\r\n        sessions: {\r\n          items: response.data.items,\r\n          total: response.data.total,\r\n        },\r\n      });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch sessions\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  createSession: async (sessionData) => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const response = await apiClient.post(\r\n        \"/counselor/counseling-sessions\",\r\n        sessionData\r\n      );\r\n      set((state) => ({\r\n        sessions: state.sessions\r\n          ? {\r\n              total: state.sessions.total + 1,\r\n              items: [...state.sessions?.items, response.data],\r\n            }\r\n          : null,\r\n      }));\r\n      toast.success(\"Session added successfully.\");\r\n      return { success: true, data: response.data };\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to create session\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      return { success: false, error: errorMessage };\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getSingleSession: async (sessionId: number, timezone: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/counselor/counseling-sessions/${sessionId}?timezone=${timezone}`\r\n      );\r\n      set({ currentSession: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch single session\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateSession: async (sessionId: number, updatedData: Partial<Session>, timezone:string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      // Get the current session to preserve the event_name\r\n      const currentSession = get().sessions?.items.find(session => session.id === sessionId);\r\n\r\n      // Ensure we're not changing the event_name\r\n      const dataToSend = {\r\n        ...updatedData,\r\n        event_name: currentSession?.event_name || updatedData.event_name\r\n      };\r\n\r\n      const response = await apiClient.put(\r\n        `/counselor/counseling-sessions/${sessionId}?timezone=${timezone}`,\r\n        dataToSend\r\n      );\r\n      const updatedSession = response.data;\r\n      set((state) => ({\r\n        sessions: state.sessions\r\n          ? {\r\n              ...state.sessions,\r\n              items: state.sessions.items.map((session) =>\r\n                session.id === sessionId ? updatedSession : session\r\n              ),\r\n            }\r\n          : null,\r\n      }));\r\n      toast.success(\"Session updated successfully.\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to update session\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteSession: async (sessionId: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/counselor/counseling-sessions/${sessionId}`);\r\n      set((state) => ({\r\n        sessions: state.sessions\r\n          ? {\r\n              total: state.sessions.total - 1,\r\n              items: state.sessions.items.filter(\r\n                (session) => session.id !== sessionId\r\n              ),\r\n            }\r\n          : null,\r\n      }));\r\n      toast.success(\"Session cancelled successfully.\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to delete session\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  fetchSessionNotes: async (sessionId: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/counselor/counseling-sessions/${sessionId}/notes`\r\n      );\r\n      set({ currentNotes: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch session notes\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  createSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post(\r\n        `/counselor/counseling-sessions/${sessionId}/notes`,\r\n        data\r\n      );\r\n      set({ currentNotes: response.data });\r\n      toast.success(\"Notes added successfully.\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to create session notes\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  updateSessionNotes: async (sessionId: number, data: NotesPayload) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.put(\r\n        `/counselor/counseling-sessions/${sessionId}/notes`,\r\n        data\r\n      );\r\n      set({ currentNotes: response.data });\r\n      toast.success(\"Notes updated successfully.\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to update session notes\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  deleteSessionNotes: async (sessionId: number) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      await apiClient.delete(`/sessions/${sessionId}/notes`);\r\n      set({ currentNotes: null });\r\n      toast.success(\"Notes deleted successfully.\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to delete session notes\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  getStudentsFeedback: async (counselorId: number) => {\r\n    try {\r\n      const response = await apiClient.get(\r\n        `/counselors/${counselorId}/reviews`\r\n      );\r\n      set({ studentsFeedback: response.data });\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to fetch students feedback\";\r\n      set({ error: errorMessage });\r\n      toast.error(errorMessage);\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AAMA;AAPA;AAFA;;;;AAWO,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QAC9D,SAAS;QACT,OAAO;QACP,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAElB,eAAe,OAAO,UAAU;YAC9B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU,EAAE;oBAC1F;gBACF;gBACA,IAAI;oBACF,UAAU;wBACR,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC5B;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,kCACA;gBAEF,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,GACpB;4BACE,OAAO,MAAM,QAAQ,CAAC,KAAK,GAAG;4BAC9B,OAAO;mCAAI,MAAM,QAAQ,EAAE;gCAAO,SAAS,IAAI;6BAAC;wBAClD,IACA;oBACN,CAAC;gBACD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;oBAAE,SAAS;oBAAM,MAAM,SAAS,IAAI;gBAAC;YAC9C,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAa;YAC/C,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,WAAmB;YAC1C,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,UAAU,UAAU,EAAE,UAAU;gBAEpE,IAAI;oBAAE,gBAAgB,SAAS,IAAI;gBAAC;YACtC,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,WAAmB,aAA+B;YACtE,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,qDAAqD;gBACrD,MAAM,iBAAiB,MAAM,QAAQ,EAAE,MAAM,KAAK,CAAA,UAAW,QAAQ,EAAE,KAAK;gBAE5E,2CAA2C;gBAC3C,MAAM,aAAa;oBACjB,GAAG,WAAW;oBACd,YAAY,gBAAgB,cAAc,YAAY,UAAU;gBAClE;gBAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,UAAU,UAAU,EAAE,UAAU,EAClE;gBAEF,MAAM,iBAAiB,SAAS,IAAI;gBACpC,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,GACpB;4BACE,GAAG,MAAM,QAAQ;4BACjB,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,UAC/B,QAAQ,EAAE,KAAK,YAAY,iBAAiB;wBAEhD,IACA;oBACN,CAAC;gBACD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,+BAA+B,EAAE,WAAW;gBACpE,IAAI,CAAC,QAAU,CAAC;wBACd,UAAU,MAAM,QAAQ,GACpB;4BACE,OAAO,MAAM,QAAQ,CAAC,KAAK,GAAG;4BAC9B,OAAO,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,CAChC,CAAC,UAAY,QAAQ,EAAE,KAAK;wBAEhC,IACA;oBACN,CAAC;gBACD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,mBAAmB,OAAO;YACxB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,UAAU,MAAM,CAAC;gBAErD,IAAI;oBAAE,cAAc,SAAS,IAAI;gBAAC;YACpC,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO,WAAmB;YAC5C,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,+BAA+B,EAAE,UAAU,MAAM,CAAC,EACnD;gBAEF,IAAI;oBAAE,cAAc,SAAS,IAAI;gBAAC;gBAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO,WAAmB;YAC5C,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,UAAU,MAAM,CAAC,EACnD;gBAEF,IAAI;oBAAE,cAAc,SAAS,IAAI;gBAAC;gBAClC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,mHAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBACrD,IAAI;oBAAE,cAAc;gBAAK;gBACzB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,qBAAqB,OAAO;YAC1B,IAAI;gBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,YAAY,EAAE,YAAY,QAAQ,CAAC;gBAEtC,IAAI;oBAAE,kBAAkB,SAAS,IAAI;gBAAC;YACxC,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/overview/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faArrows } from \"@fortawesome/free-solid-svg-icons\";\r\nimport PendingPayments from \"@/app/components/counselor/overview/popup/PendingPayments\";\r\nimport StudentsFeedback from \"@/app/components/counselor/overview/popup/StudentsFeedback\";\r\nimport EventDetailsModal from \"@/app/components/counselor/overview/popup/EventDetailsModal\";\r\nimport EditEventModal from \"@/app/components/counselor/overview/popup/EditEventModal\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport SessionCards from \"./SessionCard\";\r\nimport EarningsSummary from \"./EarningsSummary\";\r\nimport { useSessions } from \"@/app/hooks/counselor/useSessions\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { format } from \"date-fns\";\r\n\r\n//////////////////////////// MAIN COMPONENT //////////////////////////\r\nexport default function CounselorOverview() {\r\n  const { getUserInfo, userInfo } = useProfile();\r\n  const { getStudentsFeedback, studentsFeedback } = useSessions();\r\n\r\n  // Combine all popup states into one object\r\n  const [popups, setPopups] = useState({\r\n    feedback: false,\r\n    pendingPayments: false,\r\n    eventsDetails: false,\r\n    editEvent: false,\r\n  });\r\n\r\n  // Generic function to toggle a specific popup by name\r\n  const togglePopup = (popupName: keyof typeof popups) => {\r\n    setPopups((prevPopups) => ({\r\n      ...prevPopups,\r\n      [popupName]: !prevPopups[popupName],\r\n    }));\r\n  };\r\n\r\n  useEffect(() => {\r\n    getUserInfo();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (userInfo) {\r\n      getStudentsFeedback(userInfo?.user_id);\r\n    }\r\n  }, [userInfo]);\r\n\r\n  ///////////////////////////////// JSX ////////////////////////////////\r\n  return (\r\n    <>\r\n      <PendingPayments\r\n        isPopupOpen={popups.pendingPayments}\r\n        setIsPopupOpen={() => togglePopup(\"pendingPayments\")}\r\n      />\r\n\r\n      <StudentsFeedback\r\n        isPopupOpen={popups.feedback}\r\n        setIsPopupOpen={() => togglePopup(\"feedback\")}\r\n        feedbackList={studentsFeedback}\r\n      />\r\n\r\n      <EventDetailsModal\r\n        isPopupOpen={popups.eventsDetails}\r\n        setIsPopupOpen={() => togglePopup(\"eventsDetails\")}\r\n      />\r\n\r\n      <EditEventModal\r\n        isPopupOpen={popups.editEvent}\r\n        setIsPopupOpen={() => togglePopup(\"editEvent\")}\r\n      />\r\n\r\n      <div className=\"pr-3\">\r\n        <h2 className=\"text-lg font-semibold mb-2\">\r\n          Your Space to Mentor, Manage, and Grow\r\n        </h2>\r\n        <p className=\"mb-4\">\r\n          Effortlessly manage your sessions, profile, and earnings while helping\r\n          students achieve their dreams\r\n        </p>\r\n\r\n        <div className=\"split-section flex flex-col-reverse md:flex-row justify-between gap-3\">\r\n          <div className=\"left-side md:w-3/5\">\r\n            <div className=\"earnings  bg-white rounded-lg p-4 mb-3\">\r\n              <h5 className=\"mb-4\">Earnings summary</h5>\r\n              <EarningsSummary />\r\n            </div>\r\n\r\n            <div className=\"feedback bg-white rounded-lg p-4\">\r\n              <div className=\"flex justify-between items-center mb-5\">\r\n                <h5>\r\n                  Students Feedback{\" \"}\r\n                  <span className=\"text-sm text-[#9EA2B3]\">\r\n                    ({studentsFeedback?.total_reviews} reviews)\r\n                  </span>\r\n                </h5>\r\n                {studentsFeedback?.reviews.length ? (\r\n                  <button onClick={() => togglePopup(\"feedback\")}>\r\n                    View all <FontAwesomeIcon icon={faArrows} className=\"w-4\" />\r\n                  </button>\r\n                ) : (\r\n                  \"\"\r\n                )}\r\n              </div>\r\n              <div className=\"content p-4 border rounded-xl\">\r\n                {studentsFeedback?.reviews.length ? (\r\n                  <div className=\"bg-white rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer flex\">\r\n                    {/* Student Avatar */}\r\n                    <div className=\"mr-4 flex-shrink-0\">\r\n                      <img\r\n                        src={\r\n                          studentsFeedback.reviews[0].student_profile_picture ||\r\n                          generatePlaceholder(\r\n                            studentsFeedback.reviews[0].student_name.split(\r\n                              /s+/\r\n                            )[0],\r\n                            studentsFeedback.reviews[0].student_name.split(\r\n                              /s+/\r\n                            )[1]\r\n                          )\r\n                        }\r\n                        alt={studentsFeedback.reviews[0].student_name}\r\n                        className=\"w-12 h-12 rounded-full object-cover\"\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Review Content */}\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"font-medium mb-1\">\r\n                        {studentsFeedback.reviews[0].student_name}\r\n                      </div>\r\n                      {/* Star Rating */}\r\n                      <div className=\"flex mb-2\">\r\n                        {[1, 2, 3, 4, 5].map((star) => (\r\n                          <svg\r\n                            key={star}\r\n                            className={`w-4 h-4 ${\r\n                              star <= studentsFeedback.reviews[0].rating\r\n                                ? \"text-yellow-400\"\r\n                                : \"text-gray-300\"\r\n                            }`}\r\n                            fill=\"currentColor\"\r\n                            viewBox=\"0 0 20 20\"\r\n                          >\r\n                            <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\r\n                          </svg>\r\n                        ))}\r\n                      </div>\r\n\r\n                      {/* Review Comment */}\r\n                      <p className=\"text-gray-700 mb-2 line-clamp-3\">\r\n                        {studentsFeedback.reviews[0].comment}\r\n                      </p>\r\n\r\n                      {/* Review Date */}\r\n                      <p className=\"text-gray-500 text-sm\">\r\n                        {format(\r\n                          new Date(studentsFeedback.reviews[0].created_at),\r\n                          \"PPP\"\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"flex justify-center items-center h-20 text-neutral5\">\r\n                    <p>No Reviews Available</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"right-side md:w-2/5\">\r\n            <div className=\"w-full h-full bg-white rounded-xl p-4 relative\">\r\n              <div className=\"flex justify-between items-center gap-5\">\r\n                <h4>Upcoming sessions</h4>\r\n              </div>\r\n\r\n              <SessionCards />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAUA;;;AAdA;;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD;IAE5D,2CAA2C;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,WAAW;IACb;IAEA,sDAAsD;IACtD,MAAM,cAAc,CAAC;QACnB,UAAU,CAAC,aAAe,CAAC;gBACzB,GAAG,UAAU;gBACb,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,UAAU;YACrC,CAAC;IACH;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU;gBACZ,oBAAoB,UAAU;YAChC;QACF;sCAAG;QAAC;KAAS;IAEb,sEAAsE;IACtE,qBACE;;0BACE,6LAAC,0KAAA,CAAA,UAAe;gBACd,aAAa,OAAO,eAAe;gBACnC,gBAAgB,IAAM,YAAY;;;;;;0BAGpC,6LAAC,2KAAA,CAAA,UAAgB;gBACf,aAAa,OAAO,QAAQ;gBAC5B,gBAAgB,IAAM,YAAY;gBAClC,cAAc;;;;;;0BAGhB,6LAAC,4KAAA,CAAA,UAAiB;gBAChB,aAAa,OAAO,aAAa;gBACjC,gBAAgB,IAAM,YAAY;;;;;;0BAGpC,6LAAC,yKAAA,CAAA,UAAc;gBACb,aAAa,OAAO,SAAS;gBAC7B,gBAAgB,IAAM,YAAY;;;;;;0BAGpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAG3C,6LAAC;wBAAE,WAAU;kCAAO;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAO;;;;;;0DACrB,6LAAC,iKAAA,CAAA,UAAe;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAG;4DACgB;0EAClB,6LAAC;gEAAK,WAAU;;oEAAyB;oEACrC,kBAAkB;oEAAc;;;;;;;;;;;;;oDAGrC,kBAAkB,QAAQ,uBACzB,6LAAC;wDAAO,SAAS,IAAM,YAAY;;4DAAa;0EACrC,6LAAC,uKAAA,CAAA,kBAAe;gEAAC,MAAM,2KAAA,CAAA,WAAQ;gEAAE,WAAU;;;;;;;;;;;+DAGtD;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,QAAQ,uBACzB,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,KACE,iBAAiB,OAAO,CAAC,EAAE,CAAC,uBAAuB,IACnD,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,iBAAiB,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAC5C,KACD,CAAC,EAAE,EACJ,iBAAiB,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAC5C,KACD,CAAC,EAAE;gEAGR,KAAK,iBAAiB,OAAO,CAAC,EAAE,CAAC,YAAY;gEAC7C,WAAU;;;;;;;;;;;sEAKd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,EAAE,CAAC,YAAY;;;;;;8EAG3C,6LAAC;oEAAI,WAAU;8EACZ;wEAAC;wEAAG;wEAAG;wEAAG;wEAAG;qEAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;4EAEC,WAAW,CAAC,QAAQ,EAClB,QAAQ,iBAAiB,OAAO,CAAC,EAAE,CAAC,MAAM,GACtC,oBACA,iBACJ;4EACF,MAAK;4EACL,SAAQ;sFAER,cAAA,6LAAC;gFAAK,GAAE;;;;;;2EATH;;;;;;;;;;8EAeX,6LAAC;oEAAE,WAAU;8EACV,iBAAiB,OAAO,CAAC,EAAE,CAAC,OAAO;;;;;;8EAItC,6LAAC;oEAAE,WAAU;8EACV,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EACJ,IAAI,KAAK,iBAAiB,OAAO,CAAC,EAAE,CAAC,UAAU,GAC/C;;;;;;;;;;;;;;;;;yEAMR,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG;;;;;;;;;;;sDAGN,6LAAC,6JAAA,CAAA,UAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAvKwB;;QACY,0IAAA,CAAA,aAAU;QACM,2IAAA,CAAA,cAAW;;;KAFvC"}}, {"offset": {"line": 2287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}