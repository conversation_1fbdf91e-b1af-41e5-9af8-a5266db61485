const CHUNK_PUBLIC_PATH = "server/app/counselor/dashboard/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_97f32f._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__592060._.js");
runtime.loadChunk("server/chunks/ssr/app_db13a2._.js");
runtime.loadChunk("server/chunks/ssr/app_layout_tsx_ab3da7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ce97a5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_b4e556.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_d758e6.js");
runtime.loadChunk("server/chunks/ssr/app_counselor_layout_tsx_78eebd._.js");
runtime.loadChunk("server/chunks/ssr/app_counselor_dashboard_layout_tsx_53851c._.js");
runtime.loadChunk("server/chunks/ssr/_57c9b6._.js");
runtime.loadChunk("server/chunks/ssr/_ff8f6c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/counselor/dashboard/page/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/counselor/dashboard/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico [app-rsc] (static)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/counselor/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/counselor/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/counselor/dashboard/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/counselor/dashboard/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
