/* [project]/app/components/common/button/index.module.css [app-client] (css) */
.index-module__Mmy9Oa__buttonBase {
  margin-top: auto;
  margin-bottom: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: .75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: .5rem;
  padding-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  letter-spacing: .025em;
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.index-module__Mmy9Oa__buttonPrimary {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonSecondary {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonBase:disabled {
  cursor: not-allowed;
  opacity: .5;
}

.index-module__Mmy9Oa__buttonContent {
  display: flex;
  align-items: center;
  gap: .5rem;
  text-wrap: nowrap;
}

.index-module__Mmy9Oa__icon {
  width: .75rem;
}

/*# sourceMappingURL=app_components_common_button_index_module_b52d8e.css.map*/