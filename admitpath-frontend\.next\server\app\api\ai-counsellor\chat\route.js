const CHUNK_PUBLIC_PATH = "server/app/api/ai-counsellor/chat/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_f89b7f._.js");
runtime.loadChunk("server/chunks/[root of the server]__839266._.js");
runtime.loadChunk("server/chunks/node_modules_formdata-node_lib_esm_fileFromPath_02dd63.js");
runtime.loadChunk("server/chunks/_4cf411._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/ai-counsellor/chat/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/ai-counsellor/chat/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
