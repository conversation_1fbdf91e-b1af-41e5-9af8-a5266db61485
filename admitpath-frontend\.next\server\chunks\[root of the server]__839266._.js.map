{"version": 3, "sources": [], "sections": [{"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/api/ai-counsellor/chat/route.ts"], "sourcesContent": ["import { NextRequest } from \"next/server\";\nimport Groq from \"groq-sdk\";\n\nconst groq = new Groq({\n  apiKey: process.env.GROQ_API_KEY,\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { message, conversationHistory = [] } = body;\n\n    if (!process.env.GROQ_API_KEY) {\n      throw new Error(\"GROQ_API_KEY is not configured\");\n    }\n\n    if (!message?.trim()) {\n      throw new Error(\"Message cannot be empty\");\n    }\n\n    // Create a comprehensive prompt for the AI counsellor chat\n    const systemPrompt = `You are an experienced academic counsellor and educational consultant specializing in university admissions, career guidance, and academic planning. You are having a real-time text conversation with a student.\n\nYour role is to:\n- Provide helpful, encouraging, and practical advice\n- Ask clarifying questions when needed to better understand the student's situation\n- Offer specific, actionable guidance for university admissions, career planning, and academic success\n- Be supportive and understanding of student concerns and anxieties\n- Share relevant insights about different universities, programs, and career paths\n- Help students identify their strengths and areas for improvement\n- Provide guidance on application strategies, essays, interviews, and standardized tests\n\nCommunication style:\n- Be conversational and approachable, like a friendly mentor\n- Keep responses concise but informative (aim for 2-4 paragraphs)\n- Use encouraging language and acknowledge student concerns\n- Ask follow-up questions to continue the conversation naturally\n- Provide specific examples when helpful\n\nRemember: You're having a chat conversation, so be more conversational and interactive than in formal counselling sessions.`;\n\n    // Build conversation messages\n    const messages = [\n      {\n        role: \"system\" as const,\n        content: systemPrompt,\n      },\n      ...conversationHistory.map((msg: any) => ({\n        role: msg.role as \"user\" | \"assistant\",\n        content: msg.content,\n      })),\n      {\n        role: \"user\" as const,\n        content: message,\n      },\n    ];\n\n    // Create streaming completion\n    const stream = await groq.chat.completions.create({\n      messages,\n      model: \"meta-llama/llama-4-maverick-17b-128e-instruct\",\n      temperature: 0.7,\n      max_tokens: 1000,\n      top_p: 0.9,\n      stream: true,\n    });\n\n    // Create a readable stream for the response\n    const encoder = new TextEncoder();\n    const readable = new ReadableStream({\n      async start(controller) {\n        try {\n          for await (const chunk of stream) {\n            const content = chunk.choices[0]?.delta?.content || \"\";\n            if (content) {\n              const data = `data: ${JSON.stringify({ content })}\\n\\n`;\n              controller.enqueue(encoder.encode(data));\n            }\n          }\n          \n          // Send end signal\n          const endData = `data: ${JSON.stringify({ done: true })}\\n\\n`;\n          controller.enqueue(encoder.encode(endData));\n          controller.close();\n        } catch (error) {\n          console.error(\"Streaming error:\", error);\n          const errorData = `data: ${JSON.stringify({ error: \"Stream error occurred\" })}\\n\\n`;\n          controller.enqueue(encoder.encode(errorData));\n          controller.close();\n        }\n      },\n    });\n\n    return new Response(readable, {\n      headers: {\n        \"Content-Type\": \"text/event-stream\",\n        \"Cache-Control\": \"no-cache\",\n        \"Connection\": \"keep-alive\",\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"POST\",\n        \"Access-Control-Allow-Headers\": \"Content-Type\",\n      },\n    });\n  } catch (error: any) {\n    console.error(\"Error in AI chat endpoint:\", error);\n    \n    const encoder = new TextEncoder();\n    const errorStream = new ReadableStream({\n      start(controller) {\n        const errorData = `data: ${JSON.stringify({ \n          error: error.message || \"Failed to generate AI response\" \n        })}\\n\\n`;\n        controller.enqueue(encoder.encode(errorData));\n        controller.close();\n      },\n    });\n\n    return new Response(errorStream, {\n      status: 500,\n      headers: {\n        \"Content-Type\": \"text/event-stream\",\n        \"Cache-Control\": \"no-cache\",\n        \"Connection\": \"keep-alive\",\n      },\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AACA;;AAEA,MAAM,OAAO,IAAI,uJAAA,CAAA,UAAI,CAAC;IACpB,QAAQ,QAAQ,GAAG,CAAC,YAAY;AAClC;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,EAAE,GAAG;QAE9C,IAAI,CAAC,QAAQ,GAAG,CAAC,YAAY,EAAE;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,SAAS,QAAQ;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,2DAA2D;QAC3D,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;2HAkBiG,CAAC;QAExH,8BAA8B;QAC9B,MAAM,WAAW;YACf;gBACE,MAAM;gBACN,SAAS;YACX;eACG,oBAAoB,GAAG,CAAC,CAAC,MAAa,CAAC;oBACxC,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;gBACtB,CAAC;YACD;gBACE,MAAM;gBACN,SAAS;YACX;SACD;QAED,8BAA8B;QAC9B,MAAM,SAAS,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAChD;YACA,OAAO;YACP,aAAa;YACb,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QAEA,4CAA4C;QAC5C,MAAM,UAAU,IAAI;QACpB,MAAM,WAAW,IAAI,eAAe;YAClC,MAAM,OAAM,UAAU;gBACpB,IAAI;oBACF,WAAW,MAAM,SAAS,OAAQ;wBAChC,MAAM,UAAU,MAAM,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;wBACpD,IAAI,SAAS;4BACX,MAAM,OAAO,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;gCAAE;4BAAQ,GAAG,IAAI,CAAC;4BACvD,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;wBACpC;oBACF;oBAEA,kBAAkB;oBAClB,MAAM,UAAU,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;wBAAE,MAAM;oBAAK,GAAG,IAAI,CAAC;oBAC7D,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBAClC,WAAW,KAAK;gBAClB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,MAAM,YAAY,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;wBAAE,OAAO;oBAAwB,GAAG,IAAI,CAAC;oBACnF,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oBAClC,WAAW,KAAK;gBAClB;YACF;QACF;QAEA,OAAO,IAAI,SAAS,UAAU;YAC5B,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;gBACd,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;YAClC;QACF;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,MAAM,UAAU,IAAI;QACpB,MAAM,cAAc,IAAI,eAAe;YACrC,OAAM,UAAU;gBACd,MAAM,YAAY,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBACxC,OAAO,MAAM,OAAO,IAAI;gBAC1B,GAAG,IAAI,CAAC;gBACR,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;gBAClC,WAAW,KAAK;YAClB;QACF;QAEA,OAAO,IAAI,SAAS,aAAa;YAC/B,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;YAChB;QACF;IACF;AACF"}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}